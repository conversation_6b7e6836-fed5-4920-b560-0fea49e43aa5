import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import APIServices from "../service/APIService";
import { API } from "../components/constants/api_url";
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { MultiSelect } from 'primereact/multiselect';
import { SplitButton } from 'primereact/splitbutton';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import * as XLSX from 'xlsx';
import { Document, Packer, Paragraph, Table, TableCell, TableRow, WidthType, AlignmentType, HeadingLevel, TextRun } from 'docx';

// Starting year for dynamic year generation
const STARTING_YEAR = 2020;

// Base reports configuration
const baseReports = [
  { type: "Internal Framework", name: "ESG" },
  { type: "Internal Framework", name: "Customer" },
  { type: "External Framework", name: "Audit" },
];

// Chevron component for expandable sections
function Chevron({ expanded, direction = "right" }) {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 24 24"
      style={{
        transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
        transition: "transform 0.2s ease",
        marginLeft: direction === "right" ? "8px" : "0",
        marginRight: direction === "left" ? "8px" : "0",
        flexShrink: 0,
      }}
    >
      <path
        d="M8 10l4 4 4-4"
        fill="none"
        stroke="#444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

const ESGReports = () => {
    // Reportclosure states
    const [expandedCountry, setExpandedCountry] = useState({});
    const [expandedType, setExpandedType] = useState({});
    const [selectedReport, setSelectedReport] = useState({ country: "", name: "" });
    const [userRole, setUserRole] = useState(null);
    const [userCountries, setUserCountries] = useState([]);
    const [dynamicReportData, setDynamicReportData] = useState([]);

    // ESG Reports filter states
    const [selectedYear, setSelectedYear] = useState('2024');
    const [selectedCountry, setSelectedCountry] = useState('');
    const [selectedEntity, setSelectedEntity] = useState('Global');
    const [selectedPeriod, setSelectedPeriod] = useState('Monthly');
    const [selectedFramework, setSelectedFramework] = useState('Global');
    const [selectedCategory, setSelectedCategory] = useState('ESG');

    // Custom date range for reporting period

    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

    // Individual filters for specific sections
    const [energyWithdrawalYear, setEnergyWithdrawalYear] = useState('2024');
    const [waterWithdrawalCountry, setWaterWithdrawalCountry] = useState('Global');

    const selector = useSelector(state => state.user.userdetail);
    const login_data = useSelector((state) => state.user.userdetail);
    const navigate = useHistory();

    const [selectedEnergyYears, setSelectedEnergyYears] = useState([]);
    const [selectedWaterEntities, setSelectedWaterEntities] = useState([]);
    const [selectedEntities, setSelectedEntities] = useState([]);


    // Chart states with new names
    const [pieChartOptions, setPieChartOptions] = useState({});
    const [groupBarChartYear, setGroupBarChartYear] = useState({});
    const [groupBarChartEntity, setGroupBarChartEntity] = useState({});

    // API data states
    const [reportData, setReportData] = useState(null);
    const [isGeneratingReport, setIsGeneratingReport] = useState(false);
    const [entitiesByCountry, setEntitiesByCountry] = useState({});
    const [availableEntities, setAvailableEntities] = useState([]);

    // Reportclosure API integration
    useEffect(() => {
        fetchUserInfo();
    }, []);

    const fetchUserInfo = async () => {
        try {
            const roleRes = await APIServices.get(API.GetRoleByUserId(94, 112));
            const role = roleRes.data;

            const isCountryAdmin = role.some((item) => item.roles.includes(6) && item.tier2_id === 0);
            const isCorporateAdmin = role.some((item) => item.roles.includes(24) && item.tier1_id === 0);
            const filterCountry = role.filter((item) => item.roles.includes(6) && item.tier2_id === 0);

            if (isCorporateAdmin) {
                setUserRole("corporate");
            } else if (isCountryAdmin) {
                setUserRole("country");
            } else {
                setUserRole("none");
            }

            const uriString = {
                include: [
                    {
                        relation: "locationTwos",
                        scope: { include: [{ relation: "locationThrees" }] },
                    },
                ],
            };

            const promise2 = await APIServices.get(
                API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );

            const shapedSite = promise2.data
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees && locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);

            const allowedCountries = isCorporateAdmin
                ? shapedSite
                : shapedSite.filter((site) => filterCountry.map((item) => item.tier1_id).includes(site.id));

            setUserCountries(allowedCountries);

            // Fetch entities for each country
            await fetchEntitiesForCountries(allowedCountries);

            // Dynamically generate reports for each allowed country
            const dynamicReports = [];

            allowedCountries.forEach((country) => {
                baseReports.forEach((report) => {
                    dynamicReports.push({
                        Country: country.name,
                        "Type of Report": report.type,
                        "Report Name": report.name,
                    });
                });
            });

            setDynamicReportData(dynamicReports);
        } catch (err) {
            console.error("Error fetching user info:", err);
            setUserRole("none");
        }
    };

    const fetchEntitiesForCountries = async (countries) => {
        try {
            const entitiesMap = {};

            // Add Global option with countries as entities
            entitiesMap['Global'] = countries.map(country => ({
                id: country.id,
                name: country.name,
                code: country.code || country.name.substring(0, 3).toUpperCase()
            }));

            // Fetch entities for each specific country using the provided API structure
            try {
                const uriString = {
                    "include": [{
                        "relation": "locationTwos",
                        "scope": {
                            "include": [{ "relation": "locationThrees" }]
                        }
                    }]
                };

                // Use the actual API structure you provided
                const entityResponse = await APIServices.get(
                    API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
                );

                // Process each country in the response
                entityResponse.data.forEach(countryData => {
                    if (countryData.locationTwos && countryData.locationTwos.length > 0) {
                        // Extract locationTwos (cities/entities) for this country
                        const entities = countryData.locationTwos
                            .filter(locationTwo => locationTwo.locationThrees && locationTwo.locationThrees.length > 0)
                            .map(locationTwo => ({
                                id: locationTwo.id,
                                name: locationTwo.name,
                                code: locationTwo.code || locationTwo.name.substring(0, 3).toUpperCase()
                            }));

                        entitiesMap[countryData.name] = entities;
                    } else {
                        entitiesMap[countryData.name] = [];
                    }
                });

            } catch (error) {
                console.error('Error fetching entities from API:', error);
               }

            setEntitiesByCountry(entitiesMap);
        } catch (error) {
            console.error("Error fetching entities:", error);
        }
    };

 // Reportclosure 
    const filteredReportData = dynamicReportData.filter((report) => {
        const countryName = report.Country.trim().toLowerCase();
        if (userRole === "corporate") return true;
        if (userRole === "country") {
            return userCountries
                .map((c) => c.name.trim().toLowerCase())
                .includes(countryName);
        }
        return false;
    });

    const groupedData = filteredReportData.reduce((acc, item) => {
        const { Country, "Type of Report": type, "Report Name": name } = item;
        if (!acc[Country]) acc[Country] = {};
        if (!acc[Country][type]) acc[Country][type] = [];
        acc[Country][type].push(name);
        return acc;
    }, {});

    const toggleCountry = (country) =>
        setExpandedCountry((prev) => ({ ...prev, [country]: !prev[country] }));

    const toggleType = (country, type) =>
        setExpandedType((prev) => ({
            ...prev,
            [`${country}-${type}`]: !prev[`${country}-${type}`],
        }));

    const countryList = Object.entries(groupedData).filter(
        ([, types]) => Object.keys(types).length > 0
    );

    // Generate year options dynamically from starting year to current year
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let year = currentYear; year >= STARTING_YEAR; year--) {
            years.push({ label: year.toString(), value: year.toString() });
        }
        return years;
    };

    const yearOptions = generateYearOptions();

    // Dynamic country options based on user permissions
    const countryOptions = [
        { label: 'Global', value: 'Global' },
        ...userCountries.map(country => ({
            label: country.name,
            value: country.name
        }))
    ];

    // Dynamic entity options based on selected country
    const entityOptions = selectedCountry && entitiesByCountry[selectedCountry]
        ? entitiesByCountry[selectedCountry].map(entity => ({
            label: entity.name,
            value: entity.name
        }))
        : [];

    // Update available entities when country changes
    useEffect(() => {
        if (selectedCountry && entitiesByCountry[selectedCountry]) {
            setAvailableEntities(entitiesByCountry[selectedCountry]);
            // Reset entity selection when country changes
            if (entitiesByCountry[selectedCountry].length > 0) {
                setSelectedEntity(entitiesByCountry[selectedCountry][0].name);
            } else {
                setSelectedEntity('');
            }
        }
    }, [selectedCountry, entitiesByCountry]);

    const periodOptions = [
        { label: 'Monthly', value: 'Monthly' },
        { label: 'Quarterly', value: 'Quarterly' },
        { label: 'Half yearly', value: 'Half yearly' },
        { label: 'Yearly', value: 'Yearly' },
        { label: 'Custom', value: 'Custom' }
    ];

    const frameworkOptions = [
        { label: 'Global', value: 'Global' },
        { label: 'Internal Framework', value: 'Internal Framework' },
        { label: 'External Framework', value: 'External Framework' }
    ];

    const categoryOptions = [
        { label: 'ESG', value: 'ESG' },
        { label: 'Customer', value: 'Customer' },
        { label: 'External Framework', value: 'External Framework' }
    ];



    // Monthly breakdown data
    const monthlyData = [
        { month: 'Jan', value: 1141 },
        { month: 'Feb', value: 1488 },
        { month: 'Mar', value: 1349 }
    ];

    // Energy spend breakdown data
    const energySpendData = [
        { category: 'Electricity', percentage: 45, color: '#FF6B35' },
        { category: 'Natural Gas', percentage: 25, color: '#F7931E' },
        { category: 'Diesel', percentage: 15, color: '#FFD23F' },
        { category: 'Petrol', percentage: 10, color: '#06D6A0' },
        { category: 'Coal', percentage: 5, color: '#118AB2' }
    ];

    // Carbon breakdown data
    const carbonBreakdownData = [
        { source: 'Electricity', percentage: 65.89, color: '#FF6B35' },
        { source: 'Natural Gas', percentage: 21.43, color: '#F7931E' },
        { source: 'Diesel', percentage: 8.76, color: '#FFD23F' },
        { source: 'Petrol', percentage: 2.87, color: '#06D6A0' },
        { source: 'Coal', percentage: 1.05, color: '#118AB2' }
    ];

    // New multi-select filters
    const [selectedEnergyFilters, setSelectedEnergyFilters] = useState([]);
    const [selectedWaterFilters, setSelectedWaterFilters] = useState([]);

    const energyFilterOptions = [
        { label: 'Electricity', value: 'electricity' },
        { label: 'Natural Gas', value: 'natural_gas' },
        { label: 'Diesel', value: 'diesel' },
        { label: 'Petrol', value: 'petrol' },
        { label: 'Coal', value: 'coal' }
    ];

    const waterFilterOptions = [
        { label: 'Ground Water', value: 'ground_water' },
        { label: 'Surface Water', value: 'surface_water' },
        { label: 'Municipal Water', value: 'municipal_water' },
        { label: 'Recycled Water', value: 'recycled_water' }
    ];

    // Helper function to get breakdown type based on selected period
    const getBreakdownType = () => {
        switch (selectedPeriod) {
            case 'Monthly':
                return 'Monthly Breakdown';
            case 'Quarterly':
                return 'Quarterly Breakdown';
            case 'Half yearly':
                return 'Half Yearly Breakdown';
            case 'Yearly':
                return 'Yearly Summary';
            case 'Custom':
                return 'Custom Period Breakdown';
            default:
                return 'Monthly Breakdown';
        }
    };

    // Helper function to get default column headers when no data is available
    const getDefaultColumnHeaders = () => {
        switch (selectedPeriod) {
            case 'Monthly':
                return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            case 'Quarterly':
                return ['Q1', 'Q2', 'Q3', 'Q4'];
            case 'Half yearly':
                return ['H1', 'H2'];
            case 'Yearly':
                return [selectedYear];
            case 'Custom':
                return ['Custom Period'];
            default:
                return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        }
    };

    // Generate monthly breakdown based on selected period
    const generateMonthlyBreakdown = () => {
        const baseValue = 1200;
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        // Generate base monthly values for the selected year
        const baseMonthlyValues = [
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jan
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Feb
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Mar
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Apr
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // May
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jun
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jul
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Aug
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Sep
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Oct
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Nov
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4))  // Dec
        ];

        if (selectedPeriod === 'Monthly') {
            // Show all 12 months
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({
                month,
                value: baseMonthlyValues[index]
            }));
        } else if (selectedPeriod === 'Quarterly') {
            // Show 4 quarters: Q1, Q2, Q3, Q4
            return [
                {
                    month: 'Q1',
                    value: baseMonthlyValues[0] + baseMonthlyValues[1] + baseMonthlyValues[2] 
                },
                {
                    month: 'Q2',
                    value: baseMonthlyValues[3] + baseMonthlyValues[4] + baseMonthlyValues[5] 
                },
                {
                    month: 'Q3',
                    value: baseMonthlyValues[6] + baseMonthlyValues[7] + baseMonthlyValues[8]
                },
                {
                    month: 'Q4',
                    value: baseMonthlyValues[9] + baseMonthlyValues[10] + baseMonthlyValues[11]
                }
            ];
        } else if (selectedPeriod === 'Half yearly') {
            // Show 2 halves: H1 (Jan-Jun), H2 (Jul-Dec)
            return [
                {
                    month: 'H1',
                    value: baseMonthlyValues.slice(0, 6).reduce((sum, val) => sum + val, 0) // Jan-Jun
                },
                {
                    month: 'H2',
                    value: baseMonthlyValues.slice(6, 12).reduce((sum, val) => sum + val, 0) // Jul-Dec
                }
            ];
        } else if (selectedPeriod === 'Yearly') {
            // Show 1 year total
            return [{
                month: selectedYear,
                value: baseMonthlyValues.reduce((sum, val) => sum + val, 0) // Sum of all 12 months
            }];
        } else if (selectedPeriod === 'Custom' && startDate && endDate) {
            // Generate months between start and end date
            const start = new Date(startDate);
            const end = new Date(endDate);
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            const months = [];
            const current = new Date(start.getFullYear(), start.getMonth(), 1);
            const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

            while (current <= endMonth) {
                const monthIndex = current.getMonth();
                months.push({
                    month: monthNames[monthIndex],
                    value: baseMonthlyValues[monthIndex]
                });
                current.setMonth(current.getMonth() + 1);
            }
            return months;
        } else {
            // Default to all 12 months if no valid period selected
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({
                month,
                value: baseMonthlyValues[index]
            }));
        }
    };

    // Dynamic data calculation based on filters
    const calculateDynamicData = () => {
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        // Calculate period multiplier based on new period options
        let periodMultiplier = 1.0;
        if (selectedPeriod === 'Monthly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Quarterly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Half yearly') periodMultiplier = 0.5;
        else if (selectedPeriod === 'Yearly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Custom') periodMultiplier = 0.5;

        // Calculate renewable energy percentage based on country and year
        const renewableBase = selectedCountry === 'Global' ? 50 : selectedCountry === 'India' ? 45 : selectedCountry === 'Singapore' ? 65 : 55;
        const renewableYearAdjustment = selectedYear === '2024' ? 5 : selectedYear === '2023' ? 0 : -5;
        const renewablePercentage = Math.min(100, Math.max(0, renewableBase + renewableYearAdjustment));

        const dynamicMonthlyData = generateMonthlyBreakdown();

        return {
            itLoad: Math.round(7378 * baseMultiplier * countryMultiplier),
            fuelConsumption: Math.round(5567 * baseMultiplier * countryMultiplier),
            waterConsumption: Math.round(7647 * baseMultiplier * countryMultiplier * periodMultiplier),
            renewablePercentage: renewablePercentage,
            monthlyData: dynamicMonthlyData
        };
    };



    // Reusable chart generation functions
    const generateGroupBarChart = (config) => {
        const {
            data,
            categories,
            yAxisTitle,
            height = 400,
            colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
        } = config;

        return {
            chart: {
                type: 'column',
                height: height
            },
            title: {
                text: null
            },
            xAxis: {
                categories: categories,
                crosshair: true
            },
            yAxis: {
                min: 0,
                title: {
                    text: yAxisTitle
                }
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    groupPadding: 0.1
                }
            },
            legend: {
                enabled: data.length > 1,
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: data.map((series, index) => ({
                ...series,
                color: colors[index % colors.length]
            }))
        };
    };

    const generateDataSeries = (entities, baseValue, multiplierFn, categories) => {
        return entities.map((entity) => {
            const multiplier = multiplierFn(entity);
            const data = categories.map(() =>
                Math.round(baseValue * multiplier * (0.8 + Math.random() * 0.4))
            );
            return {
                name: entity,
                data: data
            };
        });
    };

    // Initialize charts and set default values
    useEffect(() => {
        // Initialize charts with empty/placeholder data
        const emptyPieChart = {
            chart: { type: 'pie', height: 300 },
            title: { text: null },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: [{
                name: 'Energy Spend',
                colorByPoint: true,
                data: []
            }]
        };

        const emptyBarChart = {
            chart: { type: 'column', height: 400 },
            title: { text: null },
            xAxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                crosshair: true
            },
            yAxis: {
                min: 0,
                title: { text: 'Value' }
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    groupPadding: 0.1
                }
            },
            legend: {
                enabled: false,
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: []
        };

        setPieChartOptions(emptyPieChart);
        setGroupBarChartYear(emptyBarChart);
        setGroupBarChartEntity({...emptyBarChart, yAxis: { ...emptyBarChart.yAxis, title: { text: 'Water Withdrawal (m³)' }}});

        // Set default country and entities when user countries are loaded
        if (userCountries.length > 0 && !selectedCountry) {
            setSelectedCountry(userCountries[0].name);
        }
        if (userCountries.length > 0 && selectedWaterEntities.length === 0) {
            setSelectedWaterEntities([userCountries[0].name]);
        }
    }, [userCountries, selectedCountry, selectedWaterEntities]);



    // Backend API URL
    const api_url = "https://api.eisqr.com/stt-report/generate-report";

    // Indicator name to number mapping
    const getIndicatorNumber = (indicatorName) => {
        const indicatorMap = {
            "Customer IT Load": "264",
            "Fuel consumption": "304",
            "Water Withdrawal": "285",
            "Water Discharged": "286",
            "Imported Electricity from Grid and Renewables": "287",
            "Non-hazardous Waste Disposal": "307",
            "Refrigerants and SF6 Top-up": "305",
            "Water Withdrawn": "285"
        };
        return indicatorMap[indicatorName] || indicatorName;
    };

    // DCF name to number mapping
    const getDCFNumber = (dcfName) => {
        const dcfMap = {
            "Fuel consumption": 304,
            "Refrigerants and SF6 Top-up": 305,
            "Customer IT Load": 264,
            "Water Withdrawal": 285,
            "Water Discharged": 286,
            "Non-hazardous Waste Disposal": 307,
            "Imported Electricity from Grid and Renewables": 287
        };
        return dcfMap[dcfName] || dcfName;
    };

    // Convert entity names to proper format with locationTwoId
    const getEntityIds = (entityNames) => {
        const entityIds = [];

        entityNames.forEach(entityName => {
            // Find the entity in the entitiesByCountry mapping
            const countryEntities = entitiesByCountry[selectedCountry] || [];
            const entity = countryEntities.find(e => e.name === entityName);

            if (entity && entity.id) {
               entityIds.push(`2-${entity.id}`);
            } else {
               console.warn(`Entity ID not found for: ${entityName}`);
                entityIds.push(entityName);
            }
        });

        return entityIds;
    };

    // Backend integration - Generate JSON structures for each question
    const generateQuestionJSONStructures = () => {
        const entityNames = selectedEntities.length > 0 ? selectedEntities : [selectedEntity || selectedCountry];
        const formattedEntityIds = getEntityIds(entityNames);

        // Common applied_specific_filter structure
        const appliedSpecificFilter = {
            year: [selectedYear],
            reporting_period: selectedPeriod,
            reporting_period_from: selectedPeriod === 'Custom' && startDate ?
                `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}` : "2025-01",
            reporting_period_to: selectedPeriod === 'Custom' && endDate ?
                `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}` : "2025-05",
            entity: formattedEntityIds
        };

        const questionStructures = [];

        // Question 1: Customer IT Load
        questionStructures.push({
            main_data_source: "quantitative",
            type_of_data: "direct_extract",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: [getDCFNumber("Customer IT Load")], // 264
                sub_status: "live",
                breakdown: false
            },
            indicator_parameters: {
                indicator_name: [getIndicatorNumber("Customer IT Load")], // 264
                breakdown: false,
                breakdown_data: [301]
            },
            filter_type: "applied_specific_filter",
            applied_specific_filter: appliedSpecificFilter,
            type_of_format: "tabular_form_data",
            table_config: {
                entity_breakdown: true,
                dcf_breakdown: false
            },
            query_details: {
                query_type: "sum"
            }
        });

        // Question 3: Fuel Consumption
        questionStructures.push({
            main_data_source: "quantitative",
            type_of_data: "direct_extract",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: [getDCFNumber("Fuel consumption")], // 304
                sub_status: "live",
                breakdown: false
            },
            indicator_parameters: {
                indicator_name: [getIndicatorNumber("Fuel consumption")], // 304
                breakdown: false,
                breakdown_data: [301]
            },
            filter_type: "applied_specific_filter",
            applied_specific_filter: appliedSpecificFilter,
            type_of_format: "tabular_form_data",
            table_config: {
                entity_breakdown: true,
                dcf_breakdown: false
            },
            query_details: {
                query_type: "sum"
            }
        });

        // Question 4: Renewable Energy Breakdown
        questionStructures.push({
            main_data_source: "quantitative",
            type_of_data: "direct_extract",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: [getDCFNumber("Imported Electricity from Grid and Renewables")], // 287
                sub_status: "live",
                breakdown: false
            },
            indicator_parameters: {
                indicator_name: [getIndicatorNumber("Imported Electricity from Grid and Renewables")], // 287
                breakdown: false,
                breakdown_data: [301]
            },
            filter_type: "applied_specific_filter",
            applied_specific_filter: appliedSpecificFilter,
            type_of_format: "tabular_form_data",
            table_config: {
                entity_breakdown: true,
                dcf_breakdown: false
            },
            query_details: {
                query_type: "sum"
            }
        });

        // Question 2: Water Withdrawn
        questionStructures.push({
            main_data_source: "quantitative",
            type_of_data: "direct_extract",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: [getDCFNumber("Water Withdrawal")], 
                sub_status: "live",
                breakdown: false
            },
            indicator_parameters: {
                indicator_name: [getIndicatorNumber("Water Withdrawn")], 
                breakdown: false,
                breakdown_data: [301]
            },
            filter_type: "applied_specific_filter",
            applied_specific_filter: appliedSpecificFilter,
            type_of_format: "tabular_form_data",
            table_config: {
                entity_breakdown: true,
                dcf_breakdown: false
            },
            query_details: {
                query_type: "sum"
            }
        });

        // Log the generated JSON structure for debugging
        console.log('Generated JSON Structure for Backend API:');
        console.log('Applied Specific Filter:', appliedSpecificFilter);
        console.log('Question Structures:', JSON.stringify(questionStructures, null, 2));

        return questionStructures;
    };

    const generateReport = async () => {
        if (!selectedYear || !selectedCountry || !selectedPeriod) {
            alert('Please select Year, Country, and Reporting Period');
            return;
        }

        setIsGeneratingReport(true);

        try {
            // Generate JSON structures for all questions
            const questionStructures = generateQuestionJSONStructures();

            console.log('Generated JSON structures for questions:', questionStructures);
            console.log('JSON structures for backend API:', JSON.stringify(questionStructures, null, 2));

            // Call backend API with the question structures
            const apiResponse = await callBackendAPI(questionStructures);

            // Process the response and map it to questions
            const processedReportData = processBackendResponse(apiResponse);
            setReportData(processedReportData);

            // Update charts with new data
            updateChartsWithReportData(processedReportData);

        } catch (error) {
            console.error('Error generating report:', error);
            alert('Error generating report. Please try again.');
        } finally {
            setIsGeneratingReport(false);
        }
    };

    /**
     * Call backend API with question structures
     *
     * Expected backend response format:
     * {
     *   results: [
     *     {
     *       questionId: 1,
     *       status: 'success' | 'error',
     *       data: { value: number, unit: string, table_data?: array, percentage?: number },
     *       error?: string
     *     },
     *     ...
     *   ]
     * }
     */
    const callBackendAPI = async (questionStructures) => {
        try {
            // Use the new backend API endpoint
            const response = await APIServices.post(api_url, {
                questions: questionStructures
            });

            return response.data;
        } catch (error) {
            console.error('Backend API call failed:', error);
            // For now, return mock data if API fails
            return generateMockBackendResponse(questionStructures);
        }
    };

    // Process backend response and map to questions
    const processBackendResponse = (apiResponse) => {
        const processedData = {
            itLoad: 0,
            fuelConsumption: 0,
            waterConsumption: 0,
            renewablePercentage: 0,
            monthlyData: [],
            energyWithdrawalData: [],
            waterWithdrawalData: [],
            questionResponses: {}
        };

        // Map response data to questions
        if (apiResponse && apiResponse.results) {
            apiResponse.results.forEach(result => {
                const questionId = result.questionId;
                processedData.questionResponses[questionId] = result;

                // Map specific question responses to display data
                switch (questionId) {
                    case 1: // Customer IT Load
                        processedData.itLoad = result.data?.value || 0;
                        break;
                    case 2: // Fuel Consumption
                        processedData.fuelConsumption = result.data?.value || 0;
                        break;
                    case 3: // Water Consumption
                        processedData.waterConsumption = result.data?.value || 0;
                        if (result.data?.table_data) {
                            processedData.monthlyData = result.data.table_data;
                        }
                        break;
                    case 6: // Renewable Energy
                        processedData.renewablePercentage = result.data?.percentage || 0;
                        break;
                    case 7: // Water Withdrawn
                        if (result.data?.table_data) {
                            processedData.waterWithdrawalData = result.data.table_data;
                        }
                        break;
                    default:
                        break;
                }
            });
        }

        return processedData;
    };

    // Generate mock backend response for testing
    const generateMockBackendResponse = (questionStructures) => {
        const results = questionStructures.map(question => {
            const countryMultiplier = getCountryMultiplier(selectedCountry);

            switch (question.questionId) {
                case 1:
                    return {
                        questionId: 1,
                        status: 'success',
                        data: {
                            value: Math.round(7378 * countryMultiplier),
                            unit: 'MWh'
                        }
                    };
                case 2:
                    return {
                        questionId: 2,
                        status: 'success',
                        data: {
                            value: Math.round(5567 * countryMultiplier),
                            unit: 'L'
                        }
                    };
                case 3:
                    return {
                        questionId: 3,
                        status: 'success',
                        data: {
                            value: Math.round(7647 * countryMultiplier),
                            unit: 'm³',
                            table_data: generateDynamicMonthlyData({
                                year: selectedYear,
                                country: selectedCountry,
                                period: selectedPeriod
                            })
                        }
                    };
                case 6:
                    return {
                        questionId: 6,
                        status: 'success',
                        data: {
                            percentage: Math.min(100, Math.max(0, 50 + (parseInt(selectedYear) - 2020) * 2))
                        }
                    };
                case 7:
                    return {
                        questionId: 7,
                        status: 'success',
                        data: {
                            table_data: generateDataSeries(
                                [selectedCountry],
                                11090,
                                (entity) => getCountryMultiplier(entity),
                                ['2020', '2021', '2022', '2023', '2024']
                            )
                        }
                    };
                default:
                    return {
                        questionId: question.questionId,
                        status: 'success',
                        data: {}
                    };
            }
        });

        return { results };
    };

    const generateDynamicMonthlyData = (filters) => {
        const baseValue = 1200;
        const countryMultiplier = getCountryMultiplier(filters.country);
        const entityMultiplier = getEntityMultiplier(filters.entity);
        const yearMultiplier = getYearMultiplier(filters.year);

        // Generate base monthly values
        const baseMonthlyValues = Array.from({ length: 12 }, () =>
            Math.round(baseValue * yearMultiplier * countryMultiplier * entityMultiplier * (0.8 + Math.random() * 0.4))
        );

        if (filters.period === 'Monthly') {
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({ month, value: baseMonthlyValues[index] }));
        } else if (filters.period === 'Quarterly') {
            return [
                { month: 'Q1', value: baseMonthlyValues.slice(0, 3).reduce((sum, val) => sum + val, 0) },
                { month: 'Q2', value: baseMonthlyValues.slice(3, 6).reduce((sum, val) => sum + val, 0) },
                { month: 'Q3', value: baseMonthlyValues.slice(6, 9).reduce((sum, val) => sum + val, 0) },
                { month: 'Q4', value: baseMonthlyValues.slice(9, 12).reduce((sum, val) => sum + val, 0) }
            ];
        } else if (filters.period === 'Half yearly') {
            return [
                { month: 'H1', value: baseMonthlyValues.slice(0, 6).reduce((sum, val) => sum + val, 0) },
                { month: 'H2', value: baseMonthlyValues.slice(6, 12).reduce((sum, val) => sum + val, 0) }
            ];
        } else if (filters.period === 'Yearly') {
            return [{ month: filters.year, value: baseMonthlyValues.reduce((sum, val) => sum + val, 0) }];
        }
        return [];
    };

    const getCountryMultiplier = (country) => {
        switch (country) {
            case 'Global': return 1.0;
            case 'India': return 1.2;
            case 'Singapore': return 0.8;
            case 'UK': return 0.9;
            default: return 1.0;
        }
    };

    const getYearMultiplier = (year) => {
        switch (year) {
            case '2024': return 1.1;
            case '2023': return 1.0;
            case '2022': return 0.9;
            default: return 0.8;
        }
    };

    const getEntityMultiplier = (entity) => {
        // Entity-specific multipliers based on size/capacity
        const entityMultipliers = {
            'Ahmedabad': 1.3,
            'Chennai': 1.2,
            'Mumbai': 1.4,
            'Bangalore': 1.1,
            'Singapore Central': 1.0,
            'Singapore East': 0.8,
            'Singapore West': 0.9,
            'London': 1.2,
            'Manchester': 0.9,
            'Birmingham': 1.0,
            'Global Operations': 1.0
        };
        return entityMultipliers[entity] || 1.0;
    };



    const updateChartsWithReportData = (data) => {
        // Update pie chart
        const pieOptions = {
            chart: { type: 'pie', height: 300 },
            title: { text: null },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: [{
                name: 'Energy Spend',
                colorByPoint: true,
                data: energySpendData.map(item => ({
                    name: item.category,
                    y: item.percentage,
                    color: item.color
                }))
            }]
        };

        // Update energy withdrawal chart
        const energyChartConfig = generateGroupBarChart({
            data: data.energyWithdrawalData,
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            yAxisTitle: 'Energy (MWh)',
            height: 400
        });

        // Update water withdrawal chart
        const waterChartConfig = generateGroupBarChart({
            data: data.waterWithdrawalData,
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            yAxisTitle: 'Water Withdrawal (m³)',
            height: 380,
            colors: ['#06D6A0', '#118AB2', '#FFD23F', '#F7931E', '#FF6B35']
        });

        setPieChartOptions(pieOptions);
        setGroupBarChartYear(energyChartConfig);
        setGroupBarChartEntity(waterChartConfig);
    };

    /**
     * Generate and download report in specified format (PDF, Word, or Excel)
     */
    const downloadReport = async (format = 'pdf') => {
        if (!reportData) {
            alert('Please generate a report first before downloading.');
            return;
        }

        try {
            console.log(`Generating ${format.toUpperCase()} report...`);

            switch (format.toLowerCase()) {
                case 'pdf':
                    await generatePDFReport();
                    break;
                case 'word':
                    await generateWordReport();
                    break;
                case 'excel':
                    await generateExcelReport();
                    break;
                default:
                    await generatePDFReport();
            }

        } catch (error) {
            console.error(`Error generating ${format} report:`, error);
            alert(`Error generating ${format} report. Please try again.`);
        }
    };

    /**
     * Generate PDF report
     */
    const generatePDFReport = async () => {
        try {
            // Set up pdfMake fonts
            pdfMake.vfs = pdfFonts.pdfMake.vfs;

            // Generate chart images
            const chartImages = await generateAllChartImages();

            // Prepare Questions-Response table data
            const questionsTableBody = [
                ['Questions', 'Response'], // Header row
                [
                    `Total Customer IT Load for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}`,
                    `${reportData ? reportData.itLoad.toLocaleString() : '0'} MWh`
                ],
                [
                    `Total Fuel Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}`,
                    `${reportData ? reportData.fuelConsumption.toLocaleString() : '0'} L`
                ],
                [
                    `Renewable Energy Consumption Percentage for ${selectedCountry} for the ${selectedYear}`,
                    `${reportData ? reportData.renewablePercentage.toFixed(1) : '0.0'}%`
                ]
            ];

            // Prepare Water Consumption table data
            const waterTableHeaders = ['Question'];
            const dataHeaders = reportData && reportData.monthlyData ?
                reportData.monthlyData.map(month => selectedPeriod === 'Yearly' ? selectedYear : month.month) :
                getDefaultColumnHeaders();
            waterTableHeaders.push(...dataHeaders);

            const waterTableBody = [
                waterTableHeaders, // Header row
                [
                    'Total Water Consumption (m³)',
                    ...(reportData && reportData.monthlyData ?
                        reportData.monthlyData.map(month => month.value.toLocaleString()) :
                        getDefaultColumnHeaders().map(() => '-'))
                ]
            ];

            // Create PDF document definition
            const docDefinition = {
                pageSize: 'A4',
                pageOrientation: dataHeaders.length > 6 ? 'landscape' : 'portrait',
                pageMargins: [40, 60, 40, 60],
                content: [
                    // Header
                    {
                        text: 'ESG Report',
                        style: 'header',
                        alignment: 'center',
                        margin: [0, 0, 0, 10]
                    },
                    {
                        text: 'Global • Internal Framework • ESG',
                        style: 'subheader',
                        alignment: 'center',
                        margin: [0, 0, 0, 5]
                    },
                    {
                        text: `Generated on: ${new Date().toLocaleDateString()}`,
                        style: 'small',
                        alignment: 'center',
                        margin: [0, 0, 0, 20]
                    },

                    // Report Configuration
                    {
                        text: 'Report Configuration',
                        style: 'sectionHeader',
                        margin: [0, 0, 0, 10]
                    },
                    {
                        ul: [
                            `Year: ${selectedYear}`,
                            `Country: ${selectedCountry}`,
                            `Entity: ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)}`,
                            `Reporting Period: ${selectedPeriod}`,
                            ...(selectedPeriod === 'Custom' && startDate && endDate ?
                                [`Custom Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`] : [])
                        ],
                        margin: [0, 0, 0, 20]
                    },

                    // Questions Table
                    {
                        text: 'Questions',
                        style: 'sectionHeader',
                        margin: [0, 0, 0, 5]
                    },
                    {
                        text: 'The organization shall report on the following:',
                        style: 'normal',
                        margin: [0, 0, 0, 10]
                    },
                    {
                        table: {
                            headerRows: 1,
                            widths: ['70%', '30%'],
                            body: questionsTableBody
                        },
                        layout: {
                            fillColor: function (rowIndex) {
                                return rowIndex === 0 ? '#f8f9fa' : null;
                            }
                        },
                        margin: [0, 0, 0, 10]
                    },
                    {
                        text: 'Table 1: The following table outlines the missing submissions and entities that were expected to contribute to this report.',
                        style: 'footnote',
                        margin: [0, 0, 0, 20]
                    },

                    // Page break before Water Consumption table
                    { text: '', pageBreak: 'before' },

                    // Water Consumption Table
                    {
                        text: 'Water Consumption Breakdown',
                        style: 'sectionHeader',
                        margin: [0, 0, 0, 5]
                    },
                    {
                        text: `Total Water Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear} (${getBreakdownType()})`,
                        style: 'normal',
                        margin: [0, 0, 0, 10]
                    },
                    {
                        table: {
                            headerRows: 1,
                            widths: generateColumnWidths(dataHeaders.length),
                            body: waterTableBody
                        },
                        layout: {
                            fillColor: function (rowIndex) {
                                return rowIndex === 0 ? '#f8f9fa' : null;
                            }
                        },
                        margin: [0, 0, 0, 10]
                    },

                    // Total Summary
                    {
                        table: {
                            widths: ['*', 'auto'],
                            body: [
                                [
                                    { text: 'Total Water Consumption:', style: 'bold' },
                                    { text: `${reportData ? reportData.waterConsumption.toLocaleString() : '0'} m³`, style: 'bold' }
                                ]
                            ]
                        },
                        layout: {
                            fillColor: '#f8f9fa'
                        },
                        margin: [0, 10, 0, 10]
                    },
                    {
                        text: `Table 2: Water consumption breakdown by ${getBreakdownType().toLowerCase()} for the selected period.`,
                        style: 'footnote'
                    },

                    // Page break before Charts section
                    { text: '', pageBreak: 'before' },

                    // Charts Section Header
                    {
                        text: 'Charts and Visual Analysis',
                        style: 'sectionHeader',
                        margin: [0, 0, 0, 20]
                    },



                    // Total Energy Withdrawal Chart
                    {
                        text: 'Total Energy Withdrawal',
                        style: 'chartHeader',
                        margin: [0, 0, 0, 5]
                    },
                    {
                        text: `Energy withdrawal data for ${selectedCountry} (Monthly Breakdown)`,
                        style: 'normal',
                        margin: [0, 0, 0, 10]
                    },
                    // Energy withdrawal bar chart image
                    chartImages.energyChart ? {
                        image: chartImages.energyChart,
                        width: 520,
                        alignment: 'center',
                        margin: [0, 0, 0, 20]
                    } : {
                        text: 'Energy withdrawal chart could not be generated',
                        style: 'normal',
                        alignment: 'center',
                        margin: [0, 0, 0, 20]
                    },

                    // Total Water Withdrawal Chart
                    {
                        text: 'Total Water Withdrawal',
                        style: 'chartHeader',
                        margin: [0, 0, 0, 5]
                    },
                    {
                        text: 'Water withdrawal data by entity (Monthly Breakdown)',
                        style: 'normal',
                        margin: [0, 0, 0, 10]
                    },
                    // Water withdrawal bar chart image
                    chartImages.waterChart ? {
                        image: chartImages.waterChart,
                        width: 520,
                        alignment: 'center',
                        margin: [0, 0, 0, 10]
                    } : {
                        text: 'Water withdrawal chart could not be generated',
                        style: 'normal',
                        alignment: 'center',
                        margin: [0, 0, 0, 10]
                    },

                    // Charts Footer Note
                    {
                        text: 'Note: Chart data represents the visual information displayed in the corresponding charts on the ESG Reports page.',
                        style: 'footnote',
                        margin: [0, 10, 0, 0]
                    }
                ],
                styles: {
                    header: {
                        fontSize: 20,
                        bold: true
                    },
                    subheader: {
                        fontSize: 12
                    },
                    sectionHeader: {
                        fontSize: 14,
                        bold: true
                    },
                    chartHeader: {
                        fontSize: 12,
                        bold: true
                    },
                    normal: {
                        fontSize: 10
                    },
                    small: {
                        fontSize: 9
                    },
                    footnote: {
                        fontSize: 8,
                        italics: true
                    },
                    bold: {
                        bold: true
                    }
                }
            };

            // Generate and download PDF
            const fileName = `ESG_Report_${selectedCountry}_${selectedYear}_${new Date().toISOString().split('T')[0]}.pdf`;
            pdfMake.createPdf(docDefinition).download(fileName);

        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Error generating PDF. Please try again.');
        }
    };

    /**
     * Generate Word report
     */
    const generateWordReport = async () => {
        try {
            console.log('Starting Word report generation...');

            if (!reportData) {
                alert('Please generate a report first before downloading.');
                return;
            }

            // Create simple HTML content and convert to Word
            const htmlContent = `
                <html>
                    <head>
                        <meta charset="utf-8">
                        <title>ESG Report</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; }
                            h1 { text-align: center; color: #333; }
                            h2 { color: #666; margin-top: 30px; }
                            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                            th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                            th { background-color: #f5f5f5; font-weight: bold; }
                            .config { margin: 20px 0; }
                            .config p { margin: 5px 0; }
                        </style>
                    </head>
                    <body>
                        <h1>ESG Report</h1>
                        <p style="text-align: center;">Global • Internal Framework • ESG</p>
                        <p style="text-align: center;">Generated on: ${new Date().toLocaleDateString()}</p>

                        <div class="config">
                            <h2>Report Configuration</h2>
                            <p><strong>Year:</strong> ${selectedYear}</p>
                            <p><strong>Country:</strong> ${selectedCountry}</p>
                            <p><strong>Entity:</strong> ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)}</p>
                            <p><strong>Reporting Period:</strong> ${selectedPeriod}</p>
                        </div>

                        <h2>Questions</h2>
                        <p>The organization shall report on the following:</p>

                        <table>
                            <thead>
                                <tr>
                                    <th style="width: 70%;">Questions</th>
                                    <th style="width: 30%;">Response</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Total Customer IT Load for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}</td>
                                    <td>${reportData ? reportData.itLoad.toLocaleString() : '0'} MWh</td>
                                </tr>
                                <tr>
                                    <td>Total Fuel Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}</td>
                                    <td>${reportData ? reportData.fuelConsumption.toLocaleString() : '0'} L</td>
                                </tr>
                                <tr>
                                    <td>Renewable Energy Consumption Percentage for ${selectedCountry} for the ${selectedYear}</td>
                                    <td>${reportData ? reportData.renewablePercentage.toFixed(1) : '0.0'}%</td>
                                </tr>
                            </tbody>
                        </table>

                        <h2>Water Consumption Breakdown</h2>
                        <p>Total Water Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear} (${getBreakdownType()})</p>

                        <table>
                            <thead>
                                <tr>
                                    <th>Question</th>
                                    ${reportData && reportData.monthlyData ?
                                        reportData.monthlyData.map(month => `<th>${selectedPeriod === 'Yearly' ? selectedYear : month.month}</th>`).join('') :
                                        getDefaultColumnHeaders().map(header => `<th>${header}</th>`).join('')
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Total Water Consumption (m³)</strong></td>
                                    ${reportData && reportData.monthlyData ?
                                        reportData.monthlyData.map(month => `<td>${month.value.toLocaleString()}</td>`).join('') :
                                        getDefaultColumnHeaders().map(() => '<td>-</td>').join('')
                                    }
                                </tr>
                            </tbody>
                        </table>

                        <p><strong>Total Water Consumption:</strong> ${reportData ? reportData.waterConsumption.toLocaleString() : '0'} m³</p>
                    </body>
                </html>
            `;

            // Convert HTML to blob and download
            const blob = new Blob([htmlContent], {
                type: 'application/msword'
            });

            const fileName = `ESG_Report_${selectedCountry}_${selectedYear}_${new Date().toISOString().split('T')[0]}.doc`;

            // Create download link
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = fileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up
            setTimeout(() => {
                URL.revokeObjectURL(link.href);
            }, 100);

            console.log('Word report generated successfully');

        } catch (error) {
            console.error('Error generating Word report:', error);
            alert('Error generating Word report. Please try again.');
        }
    };

    /**
     * Generate Excel report with all tables on a single sheet
     */
    const generateExcelReport = async () => {
        try {
            console.log('Generating Excel report...');

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Single sheet with all data
            const allData = [
                // Header
                ['ESG Report'],
                ['Global • Internal Framework • ESG'],
                [`Generated on: ${new Date().toLocaleDateString()}`],
                [''],

                // Report Configuration
                ['Report Configuration'],
                [`Year: ${selectedYear}`],
                [`Country: ${selectedCountry}`],
                [`Entity: ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)}`],
                [`Reporting Period: ${selectedPeriod}`],
                [''],

                // Questions Table
                ['Questions and Responses'],
                ['Questions', 'Response'],
                [
                    `Total Customer IT Load for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}`,
                    `${reportData ? reportData.itLoad.toLocaleString() : '0'} MWh`
                ],
                [
                    `Total Fuel Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear}`,
                    `${reportData ? reportData.fuelConsumption.toLocaleString() : '0'} L`
                ],
                [
                    `Renewable Energy Consumption Percentage for ${selectedCountry} for the ${selectedYear}`,
                    `${reportData ? reportData.renewablePercentage.toFixed(1) : '0.0'}%`
                ],
                [''],

                // Water Consumption Matrix
                ['Water Consumption Breakdown'],
                [`Total Water Consumption for ${selectedEntities.length > 0 ? selectedEntities.join(', ') : (selectedEntity || selectedCountry)} for ${selectedYear} (${getBreakdownType()})`],
                ['']
            ];

            // Add water consumption matrix headers and data
            const waterHeaders = ['Question'];
            const dataHeaders = reportData && reportData.monthlyData ?
                reportData.monthlyData.map(month => selectedPeriod === 'Yearly' ? selectedYear : month.month) :
                getDefaultColumnHeaders();
            waterHeaders.push(...dataHeaders);

            allData.push(waterHeaders);
            allData.push([
                'Total Water Consumption (m³)',
                ...(reportData && reportData.monthlyData ?
                    reportData.monthlyData.map(month => month.value) :
                    getDefaultColumnHeaders().map(() => 0)
                )
            ]);
            allData.push(['']);
            allData.push(['Total Water Consumption:', reportData ? reportData.waterConsumption : 0, 'm³']);
            allData.push(['']);

            // Add Energy Withdrawal Chart Data
            if (groupBarChartYear && groupBarChartYear.series && groupBarChartYear.series.length > 0) {
                allData.push(['Total Energy Withdrawal Chart Data']);
                allData.push([`Energy withdrawal data for ${selectedCountry} (Monthly Breakdown)`]);
                allData.push(['']);
                allData.push(['Entity/Year', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']);

                groupBarChartYear.series.forEach(series => {
                    const row = [series.name || 'Unknown'];
                    if (series.data && series.data.length === 12) {
                        row.push(...series.data);
                    } else {
                        row.push(...Array(12).fill(0));
                    }
                    allData.push(row);
                });
                allData.push(['']);
            }

            // Add Water Withdrawal Chart Data
            if (groupBarChartEntity && groupBarChartEntity.series && groupBarChartEntity.series.length > 0) {
                allData.push(['Total Water Withdrawal Chart Data']);
                allData.push(['Water withdrawal data by entity (Monthly Breakdown)']);
                allData.push(['']);
                allData.push(['Entity', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']);

                groupBarChartEntity.series.forEach(series => {
                    const row = [series.name || 'Unknown'];
                    if (series.data && series.data.length === 12) {
                        row.push(...series.data);
                    } else {
                        row.push(...Array(12).fill(0));
                    }
                    allData.push(row);
                });
            }

            // Create single worksheet
            const ws = XLSX.utils.aoa_to_sheet(allData);
            XLSX.utils.book_append_sheet(wb, ws, 'ESG Report');

            // Generate and download
            const fileName = `ESG_Report_${selectedCountry}_${selectedYear}_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);

            console.log('Excel report generated successfully');

        } catch (error) {
            console.error('Error generating Excel report:', error);
            alert('Error generating Excel report. Please try again.');
        }
    };

    /**
     * Generate column widths for water consumption table based on number of columns
     */
    const generateColumnWidths = (dataColumnCount) => {
        const widths = ['25%']; // Question column width
        const remainingWidth = 75; // Remaining percentage for data columns
        const columnWidth = `${remainingWidth / dataColumnCount}%`;

        for (let i = 0; i < dataColumnCount; i++) {
            widths.push(columnWidth);
        }

        return widths;
    };

    /**
     * Generate all chart images for PDF by capturing existing charts from DOM
     */
    const generateAllChartImages = async () => {
        const images = {};

        try {
            console.log('Starting chart image generation from existing DOM charts...');

            // Find existing chart containers in the DOM
            let energyChartContainer = null;
            let waterChartContainer = null;

            // Find charts by looking for specific headings
            const allH4Elements = document.querySelectorAll('h4');
            for (const h4 of allH4Elements) {
                if (h4.textContent.includes('Total Energy Withdrawal')) {
                    energyChartContainer = h4.closest('.card').querySelector('.chart-container');
                } else if (h4.textContent.includes('Total Water Withdrawal')) {
                    waterChartContainer = h4.closest('.card').querySelector('.chart-container');
                }
            }

            // Fallback: use index-based selection if text-based selection fails
            if (!energyChartContainer || !waterChartContainer) {
                console.log('Using fallback DOM selection...');
                const allChartContainers = document.querySelectorAll('.col-12 .card .chart-container');
                energyChartContainer = energyChartContainer || allChartContainers[0]; // First chart container (Energy)
                waterChartContainer = waterChartContainer || allChartContainers[1];  // Second chart container (Water)

                console.log('Found chart containers:', {
                    energy: !!energyChartContainer,
                    water: !!waterChartContainer,
                    total: allChartContainers.length
                });
            }

            if (energyChartContainer) {
                console.log('Found energy chart container, capturing...');
                images.energyChart = await captureChartFromDOM(energyChartContainer, 'energy');
                console.log('Energy chart capture result:', images.energyChart ? 'Success' : 'Failed');
            } else {
                console.warn('Energy chart container not found in DOM');
            }

            if (waterChartContainer) {
                console.log('Found water chart container, capturing...');
                images.waterChart = await captureChartFromDOM(waterChartContainer, 'water');
                console.log('Water chart capture result:', images.waterChart ? 'Success' : 'Failed');
            } else {
                console.warn('Water chart container not found in DOM');
            }

            // Fallback: Generate from chart options if DOM capture fails
            if (!images.energyChart && groupBarChartYear && groupBarChartYear.series && groupBarChartYear.series.length > 0) {
                console.log('DOM capture failed for energy chart, using chart options...');
                images.energyChart = await generateChartImageFromOptions(groupBarChartYear, 'energy');
            }

            if (!images.waterChart && groupBarChartEntity && groupBarChartEntity.series && groupBarChartEntity.series.length > 0) {
                console.log('DOM capture failed for water chart, using chart options...');
                images.waterChart = await generateChartImageFromOptions(groupBarChartEntity, 'water');
            }

            console.log('Chart generation completed. Results:', {
                energyChart: !!images.energyChart,
                waterChart: !!images.waterChart
            });

        } catch (error) {
            console.error('Error generating chart images:', error);
        }

        return images;
    };

    /**
     * Capture chart image directly from existing DOM element
     */
    const captureChartFromDOM = async (chartContainer, chartType) => {
        return new Promise((resolve) => {
            try {
                console.log(`Capturing ${chartType} chart from DOM...`);

                // Find the Highcharts container within the chart container
                const highchartsContainer = chartContainer.querySelector('.highcharts-container');

                if (!highchartsContainer) {
                    console.warn(`Highcharts container not found for ${chartType} chart`);
                    resolve(null);
                    return;
                }

                // Get the SVG element
                const svgElement = highchartsContainer.querySelector('svg');

                if (!svgElement) {
                    console.warn(`SVG element not found for ${chartType} chart`);
                    resolve(null);
                    return;
                }

                // Clone the SVG to avoid modifying the original
                const clonedSvg = svgElement.cloneNode(true);

                // Set explicit dimensions and background
                clonedSvg.setAttribute('width', '700');
                clonedSvg.setAttribute('height', '400');
                clonedSvg.style.backgroundColor = '#ffffff';

                // Add white background rectangle
                const bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                bgRect.setAttribute('width', '100%');
                bgRect.setAttribute('height', '100%');
                bgRect.setAttribute('fill', '#ffffff');
                clonedSvg.insertBefore(bgRect, clonedSvg.firstChild);

                // Convert SVG to string
                const serializer = new XMLSerializer();
                const svgString = serializer.serializeToString(clonedSvg);

                // Create canvas for conversion
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 700;
                canvas.height = 400;

                // Create image from SVG
                const img = new Image();

                img.onload = function() {
                    try {
                        // Fill white background
                        ctx.fillStyle = '#ffffff';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);

                        // Draw the chart
                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                        // Convert to data URL
                        const dataURL = canvas.toDataURL('image/png', 1.0);

                        console.log(`${chartType} chart captured successfully from DOM`);
                        resolve(dataURL);
                    } catch (error) {
                        console.error(`Error converting ${chartType} chart to image:`, error);
                        resolve(null);
                    }
                };

                img.onerror = function(error) {
                    console.error(`Error loading ${chartType} chart SVG:`, error);
                    resolve(null);
                };

                // Convert SVG to data URL
                const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(encodeURIComponent(svgString).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));
                img.src = svgDataUrl;

            } catch (error) {
                console.error(`Error capturing ${chartType} chart from DOM:`, error);
                resolve(null);
            }
        });
    };

    /**
     * Generate chart image from chart options (fallback method)
     */
    const generateChartImageFromOptions = async (chartOptions, chartType) => {
        return new Promise((resolve) => {
            try {
                console.log(`Generating ${chartType} chart from options...`);

                // Create a temporary container
                const tempDiv = document.createElement('div');
                tempDiv.style.width = '700px';
                tempDiv.style.height = '400px';
                tempDiv.style.position = 'fixed';
                tempDiv.style.top = '-1000px';
                tempDiv.style.left = '-1000px';
                tempDiv.style.backgroundColor = '#ffffff';
                tempDiv.style.zIndex = '-9999';
                document.body.appendChild(tempDiv);

                // Enhanced chart options
                const enhancedOptions = {
                    ...chartOptions,
                    chart: {
                        ...chartOptions.chart,
                        backgroundColor: '#ffffff',
                        animation: false,
                        width: 700,
                        height: 400
                    },
                    exporting: {
                        enabled: false
                    },
                    credits: {
                        enabled: false
                    },
                    plotOptions: {
                        ...chartOptions.plotOptions,
                        series: {
                            animation: false
                        }
                    }
                };

                // Create the chart
                const chart = Highcharts.chart(tempDiv, enhancedOptions);

                // Wait for chart to render
                setTimeout(() => {
                    try {
                        // Get SVG
                        const svgString = chart.getSVG({
                            chart: {
                                backgroundColor: '#ffffff'
                            }
                        });

                        // Convert to base64
                        const base64SVG = btoa(encodeURIComponent(svgString).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));
                        const dataURL = `data:image/svg+xml;base64,${base64SVG}`;

                        // Clean up
                        chart.destroy();
                        if (document.body.contains(tempDiv)) {
                            document.body.removeChild(tempDiv);
                        }

                        console.log(`${chartType} chart generated successfully from options`);
                        resolve(dataURL);

                    } catch (error) {
                        console.error(`Error generating ${chartType} chart from options:`, error);
                        chart.destroy();
                        if (document.body.contains(tempDiv)) {
                            document.body.removeChild(tempDiv);
                        }
                        resolve(null);
                    }
                }, 1000);

            } catch (error) {
                console.error(`Error in generateChartImageFromOptions for ${chartType}:`, error);
                resolve(null);
            }
        });
    };







    // Handle no access case
    if (userRole === "none") {
        return (
            <div style={{ padding: "24px", fontFamily: "Arial, sans-serif" }}>
                <h3>You are not configured as an Admin.</h3>
                <p>Please contact your Corporate Admin to get access.</p>
            </div>
        );
    }

    return (
        <div className="grid bg-smoke" style={{ minHeight: '100vh', padding: '20px' }}>
            {/* Left Sidebar Navigator - Dynamic Reportclosure Navigation */}
            <div className="col-3">
                <div className="card p-3" style={{ height: 'fit-content' }}>
                    <h3 className="text-bold fs-18 mb-3">ESG Navigator</h3>

                    {countryList.map(([country, types]) => (
                        <div key={country} className="mb-3">
                            <div
                                onClick={() => toggleCountry(country)}
                                className="text-bold fs-14 mb-2 block cur-pointer"
                                style={{ cursor: "pointer" }}
                            >
                                <span>{country}</span>
                                <Chevron expanded={expandedCountry[country]} direction="right" />
                            </div>
                            {expandedCountry[country] && (
                                <div className="ml-3">
                                    {Object.entries(types).map(([type, reports]) => {
                                        const typeKey = `${country}-${type}`;
                                        return (
                                            <div key={type} className="mb-2">
                                                <div
                                                    onClick={() => toggleType(country, type)}
                                                    className="text-bold fs-12 mb-1 block cur-pointer"
                                                    style={{ cursor: "pointer" }}
                                                >
                                                    <Chevron expanded={expandedType[typeKey]} direction="left" />
                                                    {type}
                                                </div>
                                                {expandedType[typeKey] && (
                                                    <div className="ml-3">
                                                        {reports.map((report, i) => {
                                                            const isSelected =
                                                                selectedReport.country === country &&
                                                                selectedReport.name === report;
                                                            return (
                                                                <div
                                                                    key={i}
                                                                    onClick={() => setSelectedReport({ country, name: report })}
                                                                    className={`p-2 br-5 mb-1 cur-pointer ${
                                                                        isSelected ? 'bg-navy-light' : 'hover-blue'
                                                                    }`}
                                                                >
                                                                    <span className={`fs-12 ${
                                                                        isSelected ? 'clr-navy text-bold' : ''
                                                                    }`}>
                                                                        {report}
                                                                    </span>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Main Content Area - Keep existing ESG Reports content */}
            <div className="col-9">
                <div className="card p-4">
                    {/* Header */}
                    <div className="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 className="text-bold fs-24 mb-1">ESG Report</h2>
                            <span className="clr-gray-3 fs-14">Global • Internal Framework • ESG</span>
                        </div>
                        <div className="text-right">
                            <div className="text-bold fs-18">Jun 11, 2025</div>
                            <div className="clr-gray-3 fs-12">Last Updated</div>
                        </div>
                    </div>

                    {/* Report Configuration */}
                    <div className="mb-4">
                        <h3 className="text-bold fs-18 mb-3">Report Configuration</h3>
                        <p className="clr-gray-3 fs-14 mb-3">Configure your filters and generate comprehensive ESG reports</p>

                        <div className="grid">
                            {/* First Row - Basic Filters */}
                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Year</label>
                                <Dropdown
                                    value={selectedYear}
                                    options={yearOptions}
                                    onChange={(e) => setSelectedYear(e.value)}
                                    className="w-full"
                                />
                            </div>
                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Country</label>
                                <Dropdown
                                    value={selectedCountry}
                                    options={countryOptions}
                                    onChange={(e) => setSelectedCountry(e.value)}
                                    className="w-full"
                                />
                            </div>
                           <div className="col-2">
                             <label className="text-bold fs-12 mb-2 block">Entity</label>
                        <MultiSelect
                         value={selectedEntities}
                           options={entityOptions}
                         onChange={(e) => setSelectedEntities(e.value)}
                           className="w-full"
                          disabled={!selectedCountry || entityOptions.length === 0}
                            placeholder="Select Entity"
                             display="chip"
                               />
                          </div>

                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Reporting Period</label>
                                <Dropdown
                                    value={selectedPeriod}
                                    options={periodOptions}
                                    onChange={(e) => {
                                        setSelectedPeriod(e.value);
                                        if (e.value !== 'Custom') {
                                            setStartDate(null);
                                            setEndDate(null);
                                        }
                                    }}
                                    className="w-full"
                                />
                            </div>
                            <div className="col-4">
                                <label className="text-bold fs-12 mb-2 block">&nbsp;</label>
                                <div className="flex flex-wrap gap-2 justify-content-end">
                                    <Button
                                        label={isGeneratingReport ? "Generating..." : "Generate Report"}
                                        className="p-button-primary"
                                        onClick={generateReport}
                                        loading={isGeneratingReport}
                                        disabled={isGeneratingReport}
                                    />
                                    <SplitButton
                                        label="Download"
                                        icon="pi pi-download"
                                        className="p-button-outlined"
                                        onClick={() => downloadReport('pdf')}
                                        model={[
                                            {
                                                label: 'PDF',
                                                icon: 'pi pi-file-pdf',
                                                command: () => downloadReport('pdf')
                                            },
                                            {
                                                label: 'Word',
                                                icon: 'pi pi-file-word',
                                                command: () => downloadReport('word')
                                            },
                                            {
                                                label: 'Excel',
                                                icon: 'pi pi-file-excel',
                                                command: () => downloadReport('excel')
                                            }
                                        ]}
                                        tooltip="Download Report"
                                    />
                                    <Button
                                        label="History"
                                        className="p-button-outlined"
                                    />
                                </div>
                            </div>

                            {/* Second Row - Custom Date Range (only when Custom period is selected) */}
                            {selectedPeriod === 'Custom' && (
                                <>
                                    <div className="col-3">
                                        <label className="text-bold fs-12 mb-2 block">Start Date</label>
                                        <Calendar
                                            value={startDate}
                                            onChange={(e) => {
                                                setStartDate(e.value);
                                                if (endDate && e.value && endDate < e.value) {
                                                    setEndDate(null);
                                                }
                                            }}
                                            dateFormat="mm/dd/yy"
                                            className="w-full"
                                            placeholder="Select start date"
                                            showIcon
                                            maxDate={new Date()}
                                        />
                                    </div>
                                    <div className="col-3">
                                        <label className="text-bold fs-12 mb-2 block">End Date</label>
                                        <Calendar
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.value)}
                                            dateFormat="mm/dd/yy"
                                            className="w-full"
                                            placeholder="Select end date"
                                            showIcon
                                            minDate={startDate}
                                            maxDate={new Date()}
                                            disabled={!startDate}
                                        />
                                    </div>
                                    <div className="col-6">
                                        {startDate && endDate && (
                                            <div className="mt-4 p-3 bg-green-50 br-5">
                                                <div className="flex align-items-center justify-content-between">
                                                    <span className="fs-12 text-bold">Custom Period Selected:</span>
                                                    <span className="fs-12 clr-green-600">
                                                        {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="mt-1">
                                                    <span className="fs-10 clr-green-600">
                                                        Duration: {Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))} days
                                                        ({Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 30))} month(s))
                                                    </span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Questions Matrix Table */}
                    <div className="grid">
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h3 className="text-bold fs-18 mb-3">Questions</h3>
                                <p className="clr-gray-3 fs-14 mb-3">
                                    The organization shall report on the following:
                                </p>

                                {/* Matrix Table */}
                                <div className="table-responsive">
                                    <table className="table table-bordered" style={{ width: '100%', borderCollapse: 'collapse' }}>
                                        <thead>
                                            <tr style={{ backgroundColor: '#f8f9fa' }}>
                                                <th style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    fontWeight: 'bold',
                                                    width: '60%'
                                                }}>
                                                    Questions
                                                </th>
                                                <th style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    fontWeight: 'bold',
                                                    width: '40%'
                                                }}>
                                                    Response
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    Total Customer IT Load for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear}
                                                </td>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    <span className="text-bold fs-16 clr-navy">
                                                        {reportData ? reportData.itLoad.toLocaleString() : '0'} MWh
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    Total Fuel Consumption for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear}
                                                </td>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    <span className="text-bold fs-16 clr-delete">
                                                        {reportData ? reportData.fuelConsumption.toLocaleString() : '0'} L
                                                    </span>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    Renewable Energy Consumption Percentage for {selectedCountry} for the {selectedYear}
                                                </td>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'top'
                                                }}>
                                                    <span className="text-bold fs-16 text-green-600">
                                                        {reportData ? reportData.renewablePercentage.toFixed(1) : '0.0'}%
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                {/* Table Footer Note */}
                                <div className="mt-3">
                                    <p className="fs-10 clr-gray-3 fst-italic">
                                        Table 1: The following table outlines the missing submissions and entities that were expected to contribute to this report.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Water Consumption Matrix Table */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h3 className="text-bold fs-18 mb-3">Water Consumption Breakdown</h3>
                                <p className="clr-gray-3 fs-14 mb-3">
                                    Total Water Consumption for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear} ({getBreakdownType()})
                                </p>

                                {/* Water Consumption Matrix Table */}
                                <div className="table-responsive">
                                    <table className="table table-bordered" style={{ width: '100%', borderCollapse: 'collapse' }}>
                                        <thead>
                                            <tr style={{ backgroundColor: '#f8f9fa' }}>
                                                <th style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    fontWeight: 'bold',
                                                    width: '20%'
                                                }}>
                                                    Question
                                                </th>
                                                {/* Dynamic headers based on selected period */}
                                                {reportData && reportData.monthlyData ? (
                                                    reportData.monthlyData.map((month, index) => (
                                                        <th key={index} style={{
                                                            border: '1px solid #dee2e6',
                                                            padding: '12px',
                                                            fontWeight: 'bold',
                                                            textAlign: 'center',
                                                            width: `${80 / reportData.monthlyData.length}%`
                                                        }}>
                                                            {selectedPeriod === 'Yearly' ? selectedYear : month.month}
                                                        </th>
                                                    ))
                                                ) : (
                                                    // Default headers when no data
                                                    getDefaultColumnHeaders().map((header, index) => (
                                                        <th key={index} style={{
                                                            border: '1px solid #dee2e6',
                                                            padding: '12px',
                                                            fontWeight: 'bold',
                                                            textAlign: 'center',
                                                            width: `${80 / getDefaultColumnHeaders().length}%`
                                                        }}>
                                                            {header}
                                                        </th>
                                                    ))
                                                )}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style={{
                                                    border: '1px solid #dee2e6',
                                                    padding: '12px',
                                                    verticalAlign: 'middle',
                                                    fontWeight: 'bold'
                                                }}>
                                                    Total Water Consumption (m³)
                                                </td>
                                                {/* Dynamic data cells based on selected period */}
                                                {reportData && reportData.monthlyData ? (
                                                    reportData.monthlyData.map((month, index) => (
                                                        <td key={index} style={{
                                                            border: '1px solid #dee2e6',
                                                            padding: '12px',
                                                            textAlign: 'center',
                                                            verticalAlign: 'middle'
                                                        }}>
                                                            <span className="text-bold fs-14 clr-navy">
                                                                {month.value.toLocaleString()}
                                                            </span>
                                                        </td>
                                                    ))
                                                ) : (
                                                    // Default empty cells when no data
                                                    getDefaultColumnHeaders().map((_, index) => (
                                                        <td key={index} style={{
                                                            border: '1px solid #dee2e6',
                                                            padding: '12px',
                                                            textAlign: 'center',
                                                            verticalAlign: 'middle'
                                                        }}>
                                                            <span className="clr-gray-3 fs-12">-</span>
                                                        </td>
                                                    ))
                                                )}
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                {/* Total Summary */}
                                <div className="mt-3 p-3 bg-gray-50 br-5">
                                    <div className="flex justify-content-between align-items-center">
                                        <span className="text-bold fs-14">Total Water Consumption:</span>
                                        <span className="text-bold fs-16 clr-navy">
                                            {reportData ? reportData.waterConsumption.toLocaleString() : '0'} m³
                                        </span>
                                    </div>
                                </div>

                                {/* Table Footer Note */}
                                <div className="mt-3">
                                    <p className="fs-10 clr-gray-3 fst-italic">
                                        Table 2: Water consumption breakdown by {getBreakdownType().toLowerCase()} for the selected period.
                                    </p>
                                </div>
                            </div>
                        </div>



                        {/* Renewable Energy Consumption Section */}
                        <div className="col-6">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-3">Renewable Energy Consumption for {selectedCountry} for the {selectedYear}</h4>
                            {/* Energy Spend Breakdown */}
                                <div className="mb-4">
                                    <h5 className="text-bold fs-14 mb-3">Energy Spend Breakdown</h5>
                                    <div className="chart-container">
                                        <HighchartsReact
                                            highcharts={Highcharts}
                                            options={pieChartOptions}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Carbon Breakdown Section */}
                        <div className="col-6">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-3">Carbon Breakdown</h4>
                                <div className="mb-3">
                                    {carbonBreakdownData.map((item, index) => (
                                        <div key={index} className="flex justify-content-between align-items-center mb-2">
                                            <div className="flex align-items-center">
                                                <div
                                                    className="w-1rem h-1rem br-50 mr-2"
                                                    style={{ backgroundColor: item.color }}
                                                ></div>
                                                <span className="fs-14">{item.source}</span>
                                            </div>
                                            <span className="text-bold fs-14">{item.percentage}%</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Total Energy Withdrawal Section */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-4">
                                <div className="section-header mb-4">
                                    <h4 className="text-bold fs-16">Total Energy Withdrawal for {selectedCountry}</h4>
                                    <div className="flex align-items-center gap-2">
                                        <label className="text-bold fs-12">Years:</label>
                                        <MultiSelect
                                            value={selectedEnergyYears}
                                            options={yearOptions}
                                            onChange={(e) => setSelectedEnergyYears(e.value)}
                                            placeholder="Select Years"
                                            className="filter-dropdown"
                                            style={{ minWidth: '200px' }}
                                        />
                                    </div>
                                </div>
                                <div className="chart-container text-center" style={{ height: '400px', marginBottom: '1rem' }}>
                                    <HighchartsReact
                                        highcharts={Highcharts}
                                        options={groupBarChartYear}
                                    />
                                </div>
                            </div>
                        </div>



                        {/* Total Water Withdrawal Section */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-4">
                                <div className="section-header mb-4">
                                    <h4 className="text-bold fs-16">Total Water Withdrawal</h4>
                                    <div className="flex align-items-center gap-2">
                                        <label className="text-bold fs-12">Entity:</label>
                                        <MultiSelect
                                            value={selectedWaterEntities}
                                            options={countryOptions}
                                            onChange={(e) => setSelectedWaterEntities(e.value)}
                                            placeholder="Select Entities"
                                            className="filter-dropdown"
                                            style={{ minWidth: '200px' }}
                                        />
                                    </div>
                                </div>
                                <div className="chart-container" style={{ height: '400px', marginBottom: '1rem' }}>
                                    <HighchartsReact
                                        highcharts={Highcharts}
                                        options={groupBarChartEntity}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ESGReports;
