import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import APIServices from "../service/APIService";
import { API } from "../components/constants/api_url";
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { MultiSelect } from 'primereact/multiselect';

// Starting year for dynamic year generation
const STARTING_YEAR = 2020;

// Base reports configuration
const baseReports = [
  { type: "Internal Framework", name: "ESG" },
  { type: "Internal Framework", name: "Customer" },
  { type: "External Framework", name: "Audit" },
];

// Chevron component for expandable sections
function Chevron({ expanded, direction = "right" }) {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 24 24"
      style={{
        transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
        transition: "transform 0.2s ease",
        marginLeft: direction === "right" ? "8px" : "0",
        marginRight: direction === "left" ? "8px" : "0",
        flexShrink: 0,
      }}
    >
      <path
        d="M8 10l4 4 4-4"
        fill="none"
        stroke="#444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

const ESGReports = () => {
    // Reportclosure states
    const [expandedCountry, setExpandedCountry] = useState({});
    const [expandedType, setExpandedType] = useState({});
    const [selectedReport, setSelectedReport] = useState({ country: "", name: "" });
    const [userRole, setUserRole] = useState(null);
    const [userCountries, setUserCountries] = useState([]);
    const [dynamicReportData, setDynamicReportData] = useState([]);

    // ESG Reports filter states
    const [selectedYear, setSelectedYear] = useState('2024');
    const [selectedCountry, setSelectedCountry] = useState('');
    const [selectedEntity, setSelectedEntity] = useState('Global');
    const [selectedPeriod, setSelectedPeriod] = useState('Monthly');
    const [selectedFramework, setSelectedFramework] = useState('Global');
    const [selectedCategory, setSelectedCategory] = useState('ESG');

    // Custom date range for reporting period

    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

    // Individual filters for specific sections
    const [energyWithdrawalYear, setEnergyWithdrawalYear] = useState('2024');
    const [waterWithdrawalCountry, setWaterWithdrawalCountry] = useState('Global');

    const selector = useSelector(state => state.user.userdetail);
    const login_data = useSelector((state) => state.user.userdetail);
    const navigate = useHistory();

    const [selectedEnergyYears, setSelectedEnergyYears] = useState([]);
    const [selectedWaterEntities, setSelectedWaterEntities] = useState([]);
    const [selectedEntities, setSelectedEntities] = useState([]);


    // Chart states with new names
    const [pieChartOptions, setPieChartOptions] = useState({});
    const [groupBarChartYear, setGroupBarChartYear] = useState({});
    const [groupBarChartEntity, setGroupBarChartEntity] = useState({});

    // API data states
    const [reportData, setReportData] = useState(null);
    const [isGeneratingReport, setIsGeneratingReport] = useState(false);
    const [entitiesByCountry, setEntitiesByCountry] = useState({});
    const [availableEntities, setAvailableEntities] = useState([]);

    // Reportclosure API integration
    useEffect(() => {
        fetchUserInfo();
    }, []);

    const fetchUserInfo = async () => {
        try {
            const roleRes = await APIServices.get(API.GetRoleByUserId(94, 112));
            const role = roleRes.data;

            const isCountryAdmin = role.some((item) => item.roles.includes(6) && item.tier2_id === 0);
            const isCorporateAdmin = role.some((item) => item.roles.includes(24) && item.tier1_id === 0);
            const filterCountry = role.filter((item) => item.roles.includes(6) && item.tier2_id === 0);

            if (isCorporateAdmin) {
                setUserRole("corporate");
            } else if (isCountryAdmin) {
                setUserRole("country");
            } else {
                setUserRole("none");
            }

            const uriString = {
                include: [
                    {
                        relation: "locationTwos",
                        scope: { include: [{ relation: "locationThrees" }] },
                    },
                ],
            };

            const promise2 = await APIServices.get(
                API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );

            const shapedSite = promise2.data
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees && locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);

            const allowedCountries = isCorporateAdmin
                ? shapedSite
                : shapedSite.filter((site) => filterCountry.map((item) => item.tier1_id).includes(site.id));

            setUserCountries(allowedCountries);

            // Fetch entities for each country
            await fetchEntitiesForCountries(allowedCountries);

            // Dynamically generate reports for each allowed country
            const dynamicReports = [];

            allowedCountries.forEach((country) => {
                baseReports.forEach((report) => {
                    dynamicReports.push({
                        Country: country.name,
                        "Type of Report": report.type,
                        "Report Name": report.name,
                    });
                });
            });

            setDynamicReportData(dynamicReports);
        } catch (err) {
            console.error("Error fetching user info:", err);
            setUserRole("none");
        }
    };

    const fetchEntitiesForCountries = async (countries) => {
        try {
            const entitiesMap = {};

            // Add Global option with countries as entities
            entitiesMap['Global'] = countries.map(country => ({
                id: country.id,
                name: country.name,
                code: country.code || country.name.substring(0, 3).toUpperCase()
            }));

            // Fetch entities for each specific country using the provided API structure
            try {
                const uriString = {
                    "include": [{
                        "relation": "locationTwos",
                        "scope": {
                            "include": [{ "relation": "locationThrees" }]
                        }
                    }]
                };

                // Use the actual API structure you provided
                const entityResponse = await APIServices.get(
                    API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
                );

                // Process each country in the response
                entityResponse.data.forEach(countryData => {
                    if (countryData.locationTwos && countryData.locationTwos.length > 0) {
                        // Extract locationTwos (cities/entities) for this country
                        const entities = countryData.locationTwos
                            .filter(locationTwo => locationTwo.locationThrees && locationTwo.locationThrees.length > 0)
                            .map(locationTwo => ({
                                id: locationTwo.id,
                                name: locationTwo.name,
                                code: locationTwo.code || locationTwo.name.substring(0, 3).toUpperCase()
                            }));

                        entitiesMap[countryData.name] = entities;
                    } else {
                        entitiesMap[countryData.name] = [];
                    }
                });

            } catch (error) {
                console.error('Error fetching entities from API:', error);
               }

            setEntitiesByCountry(entitiesMap);
        } catch (error) {
            console.error("Error fetching entities:", error);
        }
    };

 // Reportclosure 
    const filteredReportData = dynamicReportData.filter((report) => {
        const countryName = report.Country.trim().toLowerCase();
        if (userRole === "corporate") return true;
        if (userRole === "country") {
            return userCountries
                .map((c) => c.name.trim().toLowerCase())
                .includes(countryName);
        }
        return false;
    });

    const groupedData = filteredReportData.reduce((acc, item) => {
        const { Country, "Type of Report": type, "Report Name": name } = item;
        if (!acc[Country]) acc[Country] = {};
        if (!acc[Country][type]) acc[Country][type] = [];
        acc[Country][type].push(name);
        return acc;
    }, {});

    const toggleCountry = (country) =>
        setExpandedCountry((prev) => ({ ...prev, [country]: !prev[country] }));

    const toggleType = (country, type) =>
        setExpandedType((prev) => ({
            ...prev,
            [`${country}-${type}`]: !prev[`${country}-${type}`],
        }));

    const countryList = Object.entries(groupedData).filter(
        ([, types]) => Object.keys(types).length > 0
    );

    // Generate year options dynamically from starting year to current year
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let year = currentYear; year >= STARTING_YEAR; year--) {
            years.push({ label: year.toString(), value: year.toString() });
        }
        return years;
    };

    const yearOptions = generateYearOptions();

    // Dynamic country options based on user permissions
    const countryOptions = [
        { label: 'Global', value: 'Global' },
        ...userCountries.map(country => ({
            label: country.name,
            value: country.name
        }))
    ];

    // Dynamic entity options based on selected country
    const entityOptions = selectedCountry && entitiesByCountry[selectedCountry]
        ? entitiesByCountry[selectedCountry].map(entity => ({
            label: entity.name,
            value: entity.name
        }))
        : [];

    // Update available entities when country changes
    useEffect(() => {
        if (selectedCountry && entitiesByCountry[selectedCountry]) {
            setAvailableEntities(entitiesByCountry[selectedCountry]);
            // Reset entity selection when country changes
            if (entitiesByCountry[selectedCountry].length > 0) {
                setSelectedEntity(entitiesByCountry[selectedCountry][0].name);
            } else {
                setSelectedEntity('');
            }
        }
    }, [selectedCountry, entitiesByCountry]);

    const periodOptions = [
        { label: 'Monthly', value: 'Monthly' },
        { label: 'Quarterly', value: 'Quarterly' },
        { label: 'Half yearly', value: 'Half yearly' },
        { label: 'Yearly', value: 'Yearly' },
        { label: 'Custom', value: 'Custom' }
    ];

    const frameworkOptions = [
        { label: 'Global', value: 'Global' },
        { label: 'Internal Framework', value: 'Internal Framework' },
        { label: 'External Framework', value: 'External Framework' }
    ];

    const categoryOptions = [
        { label: 'ESG', value: 'ESG' },
        { label: 'Customer', value: 'Customer' },
        { label: 'External Framework', value: 'External Framework' }
    ];



    // Monthly breakdown data
    const monthlyData = [
        { month: 'Jan', value: 1141 },
        { month: 'Feb', value: 1488 },
        { month: 'Mar', value: 1349 }
    ];

    // Energy spend breakdown data
    const energySpendData = [
        { category: 'Electricity', percentage: 45, color: '#FF6B35' },
        { category: 'Natural Gas', percentage: 25, color: '#F7931E' },
        { category: 'Diesel', percentage: 15, color: '#FFD23F' },
        { category: 'Petrol', percentage: 10, color: '#06D6A0' },
        { category: 'Coal', percentage: 5, color: '#118AB2' }
    ];

    // Carbon breakdown data
    const carbonBreakdownData = [
        { source: 'Electricity', percentage: 65.89, color: '#FF6B35' },
        { source: 'Natural Gas', percentage: 21.43, color: '#F7931E' },
        { source: 'Diesel', percentage: 8.76, color: '#FFD23F' },
        { source: 'Petrol', percentage: 2.87, color: '#06D6A0' },
        { source: 'Coal', percentage: 1.05, color: '#118AB2' }
    ];

    // New multi-select filters
    const [selectedEnergyFilters, setSelectedEnergyFilters] = useState([]);
    const [selectedWaterFilters, setSelectedWaterFilters] = useState([]);

    const energyFilterOptions = [
        { label: 'Electricity', value: 'electricity' },
        { label: 'Natural Gas', value: 'natural_gas' },
        { label: 'Diesel', value: 'diesel' },
        { label: 'Petrol', value: 'petrol' },
        { label: 'Coal', value: 'coal' }
    ];

    const waterFilterOptions = [
        { label: 'Ground Water', value: 'ground_water' },
        { label: 'Surface Water', value: 'surface_water' },
        { label: 'Municipal Water', value: 'municipal_water' },
        { label: 'Recycled Water', value: 'recycled_water' }
    ];

    // Helper function to get breakdown type based on selected period
    const getBreakdownType = () => {
        switch (selectedPeriod) {
            case 'Monthly':
                return 'Monthly Breakdown';
            case 'Quarterly':
                return 'Quarterly Breakdown';
            case 'Half yearly':
                return 'Half Yearly Breakdown';
            case 'Yearly':
                return 'Yearly Summary';
            case 'Custom':
                return 'Custom Period Breakdown';
            default:
                return 'Monthly Breakdown';
        }
    };

    // Generate monthly breakdown based on selected period
    const generateMonthlyBreakdown = () => {
        const baseValue = 1200;
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        // Generate base monthly values for the selected year
        const baseMonthlyValues = [
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jan
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Feb
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Mar
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Apr
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // May
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jun
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Jul
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Aug
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Sep
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Oct
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4)), // Nov
            Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4))  // Dec
        ];

        if (selectedPeriod === 'Monthly') {
            // Show all 12 months
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({
                month,
                value: baseMonthlyValues[index]
            }));
        } else if (selectedPeriod === 'Quarterly') {
            // Show 4 quarters: Q1, Q2, Q3, Q4
            return [
                {
                    month: 'Q1',
                    value: baseMonthlyValues[0] + baseMonthlyValues[1] + baseMonthlyValues[2] 
                },
                {
                    month: 'Q2',
                    value: baseMonthlyValues[3] + baseMonthlyValues[4] + baseMonthlyValues[5] 
                },
                {
                    month: 'Q3',
                    value: baseMonthlyValues[6] + baseMonthlyValues[7] + baseMonthlyValues[8]
                },
                {
                    month: 'Q4',
                    value: baseMonthlyValues[9] + baseMonthlyValues[10] + baseMonthlyValues[11]
                }
            ];
        } else if (selectedPeriod === 'Half yearly') {
            // Show 2 halves: H1 (Jan-Jun), H2 (Jul-Dec)
            return [
                {
                    month: 'H1',
                    value: baseMonthlyValues.slice(0, 6).reduce((sum, val) => sum + val, 0) // Jan-Jun
                },
                {
                    month: 'H2',
                    value: baseMonthlyValues.slice(6, 12).reduce((sum, val) => sum + val, 0) // Jul-Dec
                }
            ];
        } else if (selectedPeriod === 'Yearly') {
            // Show 1 year total
            return [{
                month: selectedYear,
                value: baseMonthlyValues.reduce((sum, val) => sum + val, 0) // Sum of all 12 months
            }];
        } else if (selectedPeriod === 'Custom' && startDate && endDate) {
            // Generate months between start and end date
            const start = new Date(startDate);
            const end = new Date(endDate);
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            const months = [];
            const current = new Date(start.getFullYear(), start.getMonth(), 1);
            const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

            while (current <= endMonth) {
                const monthIndex = current.getMonth();
                months.push({
                    month: monthNames[monthIndex],
                    value: baseMonthlyValues[monthIndex]
                });
                current.setMonth(current.getMonth() + 1);
            }
            return months;
        } else {
            // Default to all 12 months if no valid period selected
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({
                month,
                value: baseMonthlyValues[index]
            }));
        }
    };

    // Dynamic data calculation based on filters
    const calculateDynamicData = () => {
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        // Calculate period multiplier based on new period options
        let periodMultiplier = 1.0;
        if (selectedPeriod === 'Monthly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Quarterly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Half yearly') periodMultiplier = 0.5;
        else if (selectedPeriod === 'Yearly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Custom') periodMultiplier = 0.5;

        // Calculate renewable energy percentage based on country and year
        const renewableBase = selectedCountry === 'Global' ? 50 : selectedCountry === 'India' ? 45 : selectedCountry === 'Singapore' ? 65 : 55;
        const renewableYearAdjustment = selectedYear === '2024' ? 5 : selectedYear === '2023' ? 0 : -5;
        const renewablePercentage = Math.min(100, Math.max(0, renewableBase + renewableYearAdjustment));

        const dynamicMonthlyData = generateMonthlyBreakdown();

        return {
            itLoad: Math.round(7378 * baseMultiplier * countryMultiplier),
            fuelConsumption: Math.round(5567 * baseMultiplier * countryMultiplier),
            waterConsumption: Math.round(7647 * baseMultiplier * countryMultiplier * periodMultiplier),
            renewablePercentage: renewablePercentage,
            monthlyData: dynamicMonthlyData
        };
    };



    // Reusable chart generation functions
    const generateGroupBarChart = (config) => {
        const {
            data,
            categories,
            yAxisTitle,
            height = 400,
            colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
        } = config;

        return {
            chart: {
                type: 'column',
                height: height
            },
            title: {
                text: null
            },
            xAxis: {
                categories: categories,
                crosshair: true
            },
            yAxis: {
                min: 0,
                title: {
                    text: yAxisTitle
                }
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    groupPadding: 0.1
                }
            },
            legend: {
                enabled: data.length > 1,
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: data.map((series, index) => ({
                ...series,
                color: colors[index % colors.length]
            }))
        };
    };

    const generateDataSeries = (entities, baseValue, multiplierFn, categories) => {
        return entities.map((entity) => {
            const multiplier = multiplierFn(entity);
            const data = categories.map(() =>
                Math.round(baseValue * multiplier * (0.8 + Math.random() * 0.4))
            );
            return {
                name: entity,
                data: data
            };
        });
    };

    // Initialize charts and set default values
    useEffect(() => {
        // Initialize charts with empty/placeholder data
        const emptyPieChart = {
            chart: { type: 'pie', height: 300 },
            title: { text: null },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: [{
                name: 'Energy Spend',
                colorByPoint: true,
                data: []
            }]
        };

        const emptyBarChart = {
            chart: { type: 'column', height: 400 },
            title: { text: null },
            xAxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                crosshair: true
            },
            yAxis: {
                min: 0,
                title: { text: 'Value' }
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    groupPadding: 0.1
                }
            },
            legend: {
                enabled: false,
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: []
        };

        setPieChartOptions(emptyPieChart);
        setGroupBarChartYear(emptyBarChart);
        setGroupBarChartEntity({...emptyBarChart, yAxis: { ...emptyBarChart.yAxis, title: { text: 'Water Withdrawal (m³)' }}});

        // Set default country and entities when user countries are loaded
        if (userCountries.length > 0 && !selectedCountry) {
            setSelectedCountry(userCountries[0].name);
        }
        if (userCountries.length > 0 && selectedWaterEntities.length === 0) {
            setSelectedWaterEntities([userCountries[0].name]);
        }
    }, [userCountries, selectedCountry, selectedWaterEntities]);



    //Backend integration
const generateQuestionJSONStructures = () => {
        const filterProperties = {
            year: [selectedYear],
            reporting_period: selectedPeriod,
            reporting_period_from: selectedPeriod === 'Custom' && startDate ?
                `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}` : "NA",
            reporting_period_to: selectedPeriod === 'Custom' && endDate ?
                `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}` : "NA",
            locked_date: "NA", // This should be fetched from filter if available
            entity: selectedEntities.length > 0 ? selectedEntities : [selectedEntity || selectedCountry],
            type_of_filter: "Common"
        };

        const questionStructures = [];

        // Question 1: Customer IT Load
        questionStructures.push({
            questionId: 1,
            main_data_source: "Quantitative",
            sub_data_source: "indicator",
            indicator_parameters: {
                indicator_name: ["Customer IT Load"],
                sub_status: "live"
            },
            filter_properties: filterProperties,
            type_of_data: "direct_extract",
            type_of_format: "value_field"
        });

        // Question 2: Fuel Consumption
        questionStructures.push({
            questionId: 2,
            main_data_source: "Quantitative",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: ["Fuel Consumption"],
                sub_status: "live"
            },
            filter_properties: filterProperties,
            type_of_data: "direct_extract",
            type_of_format: "value_field"
        });

        // Question 3: Water Consumption
        questionStructures.push({
            questionId: 3,
            main_data_source: "Quantitative",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: ["Water Consumption"],
                sub_status: "live"
            },
            filter_properties: filterProperties,
            type_of_data: "direct_extract",
            type_of_format: "tabular_form_data",
            table_config: {
                period_breakdown: selectedPeriod,
                entity_breakdown: false,
                entity_details: filterProperties.entity,
                dcf_breakdown: false
            }
        });

        // Question 6: Renewable Energy Breakdown
        questionStructures.push({
            questionId: 6,
            main_data_source: "Quantitative",
            sub_data_source: "raw",
            raw_parameters: {
                dcf_name: ["Imported Electricity from the Grid and Renewables", "Imported Electricity from the Grid and Renewables"],
                sub_status: "breakdown",
                breakdown_data: [
                    ["Imported Electricity from the Grid and Renewables", ["Renewable PPA", "Green Tariff", "Self Generation", "Others"]],
                    ["Imported Electricity from the Grid and Renewables", ["Renewable PPA", "Green Tariff", "Self Generation", "Others", "Grid Import"]]
                ]
            },
            filter_properties: {
                ...filterProperties,
                reporting_period: "Yearly",
                reporting_period_from: "NA",
                reporting_period_to: "NA",
                type_of_filter: "Common"
            },
            type_of_data: "queried_data",
            type_of_format: "value_field",
            query_details: {
                query_type: "percentage",
                sub_query_type: "NA",
                query_parameters: `${selectedCountry},Yearly,parameter_a,parameter_b`
            }
        });

        // Question 7: Water Withdrawn
        questionStructures.push({
            questionId: 7,
            main_data_source: "Quantitative",
            sub_data_source: "indicator",
            indicator_parameters: {
                indicator_name: ["Water Withdrawn"],
                sub_status: "locked",
                breakdown_data: "NA"
            },
            filter_properties: {
                year: ["2024", "2023", "2022", "2021", "2020"],
                reporting_period: "Yearly",
                reporting_period_from: "NA",
                reporting_period_to: "NA",
                locked_date: "NA",
                entity: [selectedCountry],
                type_of_filter: "Specific"
            },
            type_of_data: "queried_data",
            type_of_format: "tabular_form_data",
            query_details: {
                query_type: "sum",
                sub_query_type: "breakdown_by_period",
                query_parameters: `${selectedCountry},[year],["Water Withdrawn"]`
            },
            table_config: {
                period_breakdown: "Yearly",
                entity_breakdown: false,
                dcf_breakdown: false
            }
        });

        return questionStructures;
    };

    const generateReport = async () => {
        if (!selectedYear || !selectedCountry || !selectedPeriod) {
            alert('Please select Year, Country, and Reporting Period');
            return;
        }

        setIsGeneratingReport(true);

        try {
            // Generate JSON structures for all questions
            const questionStructures = generateQuestionJSONStructures();

            console.log('Generated JSON structures for questions:', questionStructures);
            console.log('JSON structures for backend API:', JSON.stringify(questionStructures, null, 2));

            // Call backend API with the question structures
            const apiResponse = await callBackendAPI(questionStructures);

            // Process the response and map it to questions
            const processedReportData = processBackendResponse(apiResponse);
            setReportData(processedReportData);

            // Update charts with new data
            updateChartsWithReportData(processedReportData);

        } catch (error) {
            console.error('Error generating report:', error);
            alert('Error generating report. Please try again.');
        } finally {
            setIsGeneratingReport(false);
        }
    };

    /**
     * Call backend API with question structures
     *
     * Expected backend response format:
     * {
     *   results: [
     *     {
     *       questionId: 1,
     *       status: 'success' | 'error',
     *       data: { value: number, unit: string, table_data?: array, percentage?: number },
     *       error?: string
     *     },
     *     ...
     *   ]
     * }
     */
    const callBackendAPI = async (questionStructures) => {
        try {
            // Replace with your actual backend API endpoint
            const response = await APIServices.post(API.ESGReportGeneration, {
                questions: questionStructures
            });

            return response.data;
        } catch (error) {
            console.error('Backend API call failed:', error);
            // For now, return mock data if API fails
            return generateMockBackendResponse(questionStructures);
        }
    };

    // Process backend response and map to questions
    const processBackendResponse = (apiResponse) => {
        const processedData = {
            itLoad: 0,
            fuelConsumption: 0,
            waterConsumption: 0,
            renewablePercentage: 0,
            monthlyData: [],
            energyWithdrawalData: [],
            waterWithdrawalData: [],
            questionResponses: {}
        };

        // Map response data to questions
        if (apiResponse && apiResponse.results) {
            apiResponse.results.forEach(result => {
                const questionId = result.questionId;
                processedData.questionResponses[questionId] = result;

                // Map specific question responses to display data
                switch (questionId) {
                    case 1: // Customer IT Load
                        processedData.itLoad = result.data?.value || 0;
                        break;
                    case 2: // Fuel Consumption
                        processedData.fuelConsumption = result.data?.value || 0;
                        break;
                    case 3: // Water Consumption
                        processedData.waterConsumption = result.data?.value || 0;
                        if (result.data?.table_data) {
                            processedData.monthlyData = result.data.table_data;
                        }
                        break;
                    case 6: // Renewable Energy
                        processedData.renewablePercentage = result.data?.percentage || 0;
                        break;
                    case 7: // Water Withdrawn
                        if (result.data?.table_data) {
                            processedData.waterWithdrawalData = result.data.table_data;
                        }
                        break;
                    default:
                        break;
                }
            });
        }

        return processedData;
    };

    // Generate mock backend response for testing
    const generateMockBackendResponse = (questionStructures) => {
        const results = questionStructures.map(question => {
            const countryMultiplier = getCountryMultiplier(selectedCountry);

            switch (question.questionId) {
                case 1:
                    return {
                        questionId: 1,
                        status: 'success',
                        data: {
                            value: Math.round(7378 * countryMultiplier),
                            unit: 'MWh'
                        }
                    };
                case 2:
                    return {
                        questionId: 2,
                        status: 'success',
                        data: {
                            value: Math.round(5567 * countryMultiplier),
                            unit: 'L'
                        }
                    };
                case 3:
                    return {
                        questionId: 3,
                        status: 'success',
                        data: {
                            value: Math.round(7647 * countryMultiplier),
                            unit: 'm³',
                            table_data: generateDynamicMonthlyData({
                                year: selectedYear,
                                country: selectedCountry,
                                period: selectedPeriod
                            })
                        }
                    };
                case 6:
                    return {
                        questionId: 6,
                        status: 'success',
                        data: {
                            percentage: Math.min(100, Math.max(0, 50 + (parseInt(selectedYear) - 2020) * 2))
                        }
                    };
                case 7:
                    return {
                        questionId: 7,
                        status: 'success',
                        data: {
                            table_data: generateDataSeries(
                                [selectedCountry],
                                11090,
                                (entity) => getCountryMultiplier(entity),
                                ['2020', '2021', '2022', '2023', '2024']
                            )
                        }
                    };
                default:
                    return {
                        questionId: question.questionId,
                        status: 'success',
                        data: {}
                    };
            }
        });

        return { results };
    };

    const generateDynamicMonthlyData = (filters) => {
        const baseValue = 1200;
        const countryMultiplier = getCountryMultiplier(filters.country);
        const entityMultiplier = getEntityMultiplier(filters.entity);
        const yearMultiplier = getYearMultiplier(filters.year);

        // Generate base monthly values
        const baseMonthlyValues = Array.from({ length: 12 }, () =>
            Math.round(baseValue * yearMultiplier * countryMultiplier * entityMultiplier * (0.8 + Math.random() * 0.4))
        );

        if (filters.period === 'Monthly') {
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return months.map((month, index) => ({ month, value: baseMonthlyValues[index] }));
        } else if (filters.period === 'Quarterly') {
            return [
                { month: 'Q1', value: baseMonthlyValues.slice(0, 3).reduce((sum, val) => sum + val, 0) },
                { month: 'Q2', value: baseMonthlyValues.slice(3, 6).reduce((sum, val) => sum + val, 0) },
                { month: 'Q3', value: baseMonthlyValues.slice(6, 9).reduce((sum, val) => sum + val, 0) },
                { month: 'Q4', value: baseMonthlyValues.slice(9, 12).reduce((sum, val) => sum + val, 0) }
            ];
        } else if (filters.period === 'Half yearly') {
            return [
                { month: 'H1', value: baseMonthlyValues.slice(0, 6).reduce((sum, val) => sum + val, 0) },
                { month: 'H2', value: baseMonthlyValues.slice(6, 12).reduce((sum, val) => sum + val, 0) }
            ];
        } else if (filters.period === 'Yearly') {
            return [{ month: filters.year, value: baseMonthlyValues.reduce((sum, val) => sum + val, 0) }];
        }
        return [];
    };

    const getCountryMultiplier = (country) => {
        switch (country) {
            case 'Global': return 1.0;
            case 'India': return 1.2;
            case 'Singapore': return 0.8;
            case 'UK': return 0.9;
            default: return 1.0;
        }
    };

    const getYearMultiplier = (year) => {
        switch (year) {
            case '2024': return 1.1;
            case '2023': return 1.0;
            case '2022': return 0.9;
            default: return 0.8;
        }
    };

    const getEntityMultiplier = (entity) => {
        // Entity-specific multipliers based on size/capacity
        const entityMultipliers = {
            'Ahmedabad': 1.3,
            'Chennai': 1.2,
            'Mumbai': 1.4,
            'Bangalore': 1.1,
            'Singapore Central': 1.0,
            'Singapore East': 0.8,
            'Singapore West': 0.9,
            'London': 1.2,
            'Manchester': 0.9,
            'Birmingham': 1.0,
            'Global Operations': 1.0
        };
        return entityMultipliers[entity] || 1.0;
    };

    const getCountryRenewableBase = (country) => {
        switch (country) {
            case 'India': return 45;
            case 'Singapore': return 65;
            case 'UK': return 55;
            case 'Global': return 50;
            default: return 50;
        }
    };

    const getEntityRenewableAdjustment = (entity) => {
        // Entity-specific renewable energy adjustments
        const entityAdjustments = {
            'Ahmedabad': 5,
            'Chennai': 3,
            'Mumbai': -2,
            'Bangalore': 8,
            'Singapore Central': 0,
            'Singapore East': 5,
            'Singapore West': 3,
            'London': 2,
            'Manchester': 4,
            'Birmingham': 1,
            'Global Operations': 0
        };
        return entityAdjustments[entity] || 0;
    };

    const updateChartsWithReportData = (data) => {
        // Update pie chart
        const pieOptions = {
            chart: { type: 'pie', height: 300 },
            title: { text: null },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: [{
                name: 'Energy Spend',
                colorByPoint: true,
                data: energySpendData.map(item => ({
                    name: item.category,
                    y: item.percentage,
                    color: item.color
                }))
            }]
        };

        // Update energy withdrawal chart
        const energyChartConfig = generateGroupBarChart({
            data: data.energyWithdrawalData,
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            yAxisTitle: 'Energy (MWh)',
            height: 400
        });

        // Update water withdrawal chart
        const waterChartConfig = generateGroupBarChart({
            data: data.waterWithdrawalData,
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            yAxisTitle: 'Water Withdrawal (m³)',
            height: 380,
            colors: ['#06D6A0', '#118AB2', '#FFD23F', '#F7931E', '#FF6B35']
        });

        setPieChartOptions(pieOptions);
        setGroupBarChartYear(energyChartConfig);
        setGroupBarChartEntity(waterChartConfig);
    };

    const downloadReport = () => {
        console.log('Downloading report...');
    };

    // Handle no access case
    if (userRole === "none") {
        return (
            <div style={{ padding: "24px", fontFamily: "Arial, sans-serif" }}>
                <h3>You are not configured as an Admin.</h3>
                <p>Please contact your Corporate Admin to get access.</p>
            </div>
        );
    }

    return (
        <div className="grid bg-smoke" style={{ minHeight: '100vh', padding: '20px' }}>
            {/* Left Sidebar Navigator - Dynamic Reportclosure Navigation */}
            <div className="col-3">
                <div className="card p-3" style={{ height: 'fit-content' }}>
                    <h3 className="text-bold fs-18 mb-3">ESG Navigator</h3>

                    {countryList.map(([country, types]) => (
                        <div key={country} className="mb-3">
                            <div
                                onClick={() => toggleCountry(country)}
                                className="text-bold fs-14 mb-2 block cur-pointer"
                                style={{ cursor: "pointer" }}
                            >
                                <span>{country}</span>
                                <Chevron expanded={expandedCountry[country]} direction="right" />
                            </div>
                            {expandedCountry[country] && (
                                <div className="ml-3">
                                    {Object.entries(types).map(([type, reports]) => {
                                        const typeKey = `${country}-${type}`;
                                        return (
                                            <div key={type} className="mb-2">
                                                <div
                                                    onClick={() => toggleType(country, type)}
                                                    className="text-bold fs-12 mb-1 block cur-pointer"
                                                    style={{ cursor: "pointer" }}
                                                >
                                                    <Chevron expanded={expandedType[typeKey]} direction="left" />
                                                    {type}
                                                </div>
                                                {expandedType[typeKey] && (
                                                    <div className="ml-3">
                                                        {reports.map((report, i) => {
                                                            const isSelected =
                                                                selectedReport.country === country &&
                                                                selectedReport.name === report;
                                                            return (
                                                                <div
                                                                    key={i}
                                                                    onClick={() => setSelectedReport({ country, name: report })}
                                                                    className={`p-2 br-5 mb-1 cur-pointer ${
                                                                        isSelected ? 'bg-navy-light' : 'hover-blue'
                                                                    }`}
                                                                >
                                                                    <span className={`fs-12 ${
                                                                        isSelected ? 'clr-navy text-bold' : ''
                                                                    }`}>
                                                                        {report}
                                                                    </span>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Main Content Area - Keep existing ESG Reports content */}
            <div className="col-9">
                <div className="card p-4">
                    {/* Header */}
                    <div className="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 className="text-bold fs-24 mb-1">ESG Report</h2>
                            <span className="clr-gray-3 fs-14">Global • Internal Framework • ESG</span>
                        </div>
                        <div className="text-right">
                            <div className="text-bold fs-18">Jun 11, 2025</div>
                            <div className="clr-gray-3 fs-12">Last Updated</div>
                        </div>
                    </div>

                    {/* Report Configuration */}
                    <div className="mb-4">
                        <h3 className="text-bold fs-18 mb-3">Report Configuration</h3>
                        <p className="clr-gray-3 fs-14 mb-3">Configure your filters and generate comprehensive ESG reports</p>

                        <div className="grid">
                            {/* First Row - Basic Filters */}
                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Year</label>
                                <Dropdown
                                    value={selectedYear}
                                    options={yearOptions}
                                    onChange={(e) => setSelectedYear(e.value)}
                                    className="w-full"
                                />
                            </div>
                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Country</label>
                                <Dropdown
                                    value={selectedCountry}
                                    options={countryOptions}
                                    onChange={(e) => setSelectedCountry(e.value)}
                                    className="w-full"
                                />
                            </div>
                           <div className="col-2">
                             <label className="text-bold fs-12 mb-2 block">Entity</label>
                        <MultiSelect
                         value={selectedEntities}
                           options={entityOptions}
                         onChange={(e) => setSelectedEntities(e.value)}
                           className="w-full"
                          disabled={!selectedCountry || entityOptions.length === 0}
                            placeholder="Select Entity"
                             display="chip"
                               />
                          </div>

                            <div className="col-2">
                                <label className="text-bold fs-12 mb-2 block">Reporting Period</label>
                                <Dropdown
                                    value={selectedPeriod}
                                    options={periodOptions}
                                    onChange={(e) => {
                                        setSelectedPeriod(e.value);
                                        if (e.value !== 'Custom') {
                                            setStartDate(null);
                                            setEndDate(null);
                                        }
                                    }}
                                    className="w-full"
                                />
                            </div>
                            <div className="col-4">
                                <label className="text-bold fs-12 mb-2 block">&nbsp;</label>
                                <div className="flex flex-wrap gap-2 justify-content-end">
                                    <Button
                                        label={isGeneratingReport ? "Generating..." : "Generate Report"}
                                        className="p-button-primary"
                                        onClick={generateReport}
                                        loading={isGeneratingReport}
                                        disabled={isGeneratingReport}
                                    />
                                    <Button
                                        icon="pi pi-download"
                                        className="p-button-outlined"
                                        onClick={downloadReport}
                                        tooltip="Download Report"
                                    />
                                    <Button
                                        label="History"
                                        className="p-button-outlined"
                                    />
                                </div>
                            </div>

                            {/* Second Row - Custom Date Range (only when Custom period is selected) */}
                            {selectedPeriod === 'Custom' && (
                                <>
                                    <div className="col-3">
                                        <label className="text-bold fs-12 mb-2 block">Start Date</label>
                                        <Calendar
                                            value={startDate}
                                            onChange={(e) => {
                                                setStartDate(e.value);
                                                if (endDate && e.value && endDate < e.value) {
                                                    setEndDate(null);
                                                }
                                            }}
                                            dateFormat="mm/dd/yy"
                                            className="w-full"
                                            placeholder="Select start date"
                                            showIcon
                                            maxDate={new Date()}
                                        />
                                    </div>
                                    <div className="col-3">
                                        <label className="text-bold fs-12 mb-2 block">End Date</label>
                                        <Calendar
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.value)}
                                            dateFormat="mm/dd/yy"
                                            className="w-full"
                                            placeholder="Select end date"
                                            showIcon
                                            minDate={startDate}
                                            maxDate={new Date()}
                                            disabled={!startDate}
                                        />
                                    </div>
                                    <div className="col-6">
                                        {startDate && endDate && (
                                            <div className="mt-4 p-3 bg-green-50 br-5">
                                                <div className="flex align-items-center justify-content-between">
                                                    <span className="fs-12 text-bold">Custom Period Selected:</span>
                                                    <span className="fs-12 clr-green-600">
                                                        {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="mt-1">
                                                    <span className="fs-10 clr-green-600">
                                                        Duration: {Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))} days
                                                        ({Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 30))} month(s))
                                                    </span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Main Metrics Cards */}
                    <div className="grid">
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-2">
                                    Total Customer IT Load for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear}
                                </h4>
                             <div className="flex justify-content-between align-items-center">
                                    <div>
                                     <div className="text-bold fs-36 clr-navy">
                                         {reportData ? reportData.itLoad.toLocaleString() : '0'} MWh
                                     </div>
                                        </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-2">
                                    Total Fuel Consumption for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear}
                                </h4>
                              <div className="flex justify-content-between align-items-center">
                                    <div>
                                      <div className="text-bold fs-36 clr-delete">
                                          {reportData ? reportData.fuelConsumption.toLocaleString() : '0'} L
                                      </div>
                                     </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-2">
                                    Total Water Consumption for {selectedEntity ? `${selectedEntity}, ${selectedCountry}` : selectedCountry} for {selectedYear} ({getBreakdownType()})
                                </h4>
                                <div className="flex justify-content-between align-items-center mb-4">
                                    <div>
                                        <div className="text-bold fs-36 clr-navy">
                                            {reportData ? reportData.waterConsumption.toLocaleString() : '0'} m³
                                        </div>
                                    </div>
                                </div>

                                {/* Breakdown Section */}
                                <div className="mt-4">
                                    <h5 className="text-bold fs-14 mb-3">
                                        {getBreakdownType()}
                                        {selectedPeriod === 'Custom' && startDate && endDate && (
                                            <span className="clr-gray-3 fs-12 ml-2">
                                                ({startDate.toLocaleDateString()} - {endDate.toLocaleDateString()})
                                            </span>
                                        )}
                                    </h5>
                                    <div className="grid">
                                        {reportData && reportData.monthlyData ? (
                                            reportData.monthlyData.map((month, index) => {
                                                const dataLength = reportData.monthlyData.length;
                                                let colSize = 'col-1';

                                                // Determine column size based on period type
                                                if (selectedPeriod === 'Yearly') {
                                                    colSize = 'col-12';
                                                } else if (selectedPeriod === 'Quarterly') {
                                                    colSize = 'col-3';
                                                } else if (selectedPeriod === 'Half yearly') {
                                                    colSize = 'col-6';
                                                } else if (selectedPeriod === 'Monthly') {
                                                    colSize = 'col-1';
                                                } else if (dataLength <= 3) {
                                                    colSize = 'col-4';
                                                } else if (dataLength <= 6) {
                                                    colSize = 'col-2';
                                                } else {
                                                    colSize = 'col-1';
                                                }

                                                return (
                                                    <div key={index} className={`${colSize} text-center mb-2`}>
                                                        <div className="p-3 bg-gray-50 br-5">
                                                            <div className="clr-gray-3 fs-12 mb-1">
                                                                {selectedPeriod === 'Yearly' ? selectedYear : month.month.toUpperCase()}
                                                            </div>
                                                            <div className="text-bold fs-20">{month.value.toLocaleString()}</div>
                                                        </div>
                                                    </div>
                                                );
                                            })
                                        ) : (
                                            <div className="col-12 text-center p-4">
                                                <div className="clr-gray-3 fs-14">
                                                    Click "Generate Report" to view breakdown data
                                                </div>
                                            </div>
                                        )}
                                    </div>


                                </div>
                            </div>
                        </div>



                        {/* Renewable Energy Consumption Section */}
                        <div className="col-6">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-3">Renewable Energy Consumption for {selectedCountry} for the {selectedYear}</h4>
                            {/* Energy Spend Breakdown */}
                                <div className="mb-4">
                                    <h5 className="text-bold fs-14 mb-3">Energy Spend Breakdown</h5>
                                    <div className="chart-container">
                                        <HighchartsReact
                                            highcharts={Highcharts}
                                            options={pieChartOptions}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Carbon Breakdown Section */}
                        <div className="col-6">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-3">Carbon Breakdown</h4>
                                <div className="mb-3">
                                    {carbonBreakdownData.map((item, index) => (
                                        <div key={index} className="flex justify-content-between align-items-center mb-2">
                                            <div className="flex align-items-center">
                                                <div
                                                    className="w-1rem h-1rem br-50 mr-2"
                                                    style={{ backgroundColor: item.color }}
                                                ></div>
                                                <span className="fs-14">{item.source}</span>
                                            </div>
                                            <span className="text-bold fs-14">{item.percentage}%</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Total Energy Withdrawal Section */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-4">
                                <div className="section-header mb-4">
                                    <h4 className="text-bold fs-16">Total Energy Withdrawal for {selectedCountry}</h4>
                                    <div className="flex align-items-center gap-2">
                                        <label className="text-bold fs-12">Years:</label>
                                        <MultiSelect
                                            value={selectedEnergyYears}
                                            options={yearOptions}
                                            onChange={(e) => setSelectedEnergyYears(e.value)}
                                            placeholder="Select Years"
                                            className="filter-dropdown"
                                            style={{ minWidth: '200px' }}
                                        />
                                    </div>
                                </div>
                                <div className="chart-container text-center" style={{ height: '400px', marginBottom: '1rem' }}>
                                    <HighchartsReact
                                        highcharts={Highcharts}
                                        options={groupBarChartYear}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Renewable Energy Consumption Percentage */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-3">
                                <h4 className="text-bold fs-16 mb-3">Renewable Energy Consumption Percentage for {selectedCountry} for the {selectedYear}</h4>
                                <div className="flex justify-content-between align-items-center">
                                    <div>
                                        <div className="text-bold fs-36 text-green-600">
                                            {reportData ? reportData.renewablePercentage.toFixed(1) : '0.0'}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Total Water Withdrawal Section */}
                        <div className="col-12">
                            <div className="card bg-white p-4 mb-4">
                                <div className="section-header mb-4">
                                    <h4 className="text-bold fs-16">Total Water Withdrawal</h4>
                                    <div className="flex align-items-center gap-2">
                                        <label className="text-bold fs-12">Entity:</label>
                                        <MultiSelect
                                            value={selectedWaterEntities}
                                            options={countryOptions}
                                            onChange={(e) => setSelectedWaterEntities(e.value)}
                                            placeholder="Select Entities"
                                            className="filter-dropdown"
                                            style={{ minWidth: '200px' }}
                                        />
                                    </div>
                                </div>
                                <div className="chart-container" style={{ height: '400px', marginBottom: '1rem' }}>
                                    <HighchartsReact
                                        highcharts={Highcharts}
                                        options={groupBarChartEntity}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ESGReports;
