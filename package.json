{"name": "eisqr", "version": "1.0.0", "homepage": "/", "private": false, "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^5.7.2", "@fullcalendar/daygrid": "^5.7.2", "@fullcalendar/interaction": "^5.7.2", "@fullcalendar/react": "^5.7.0", "@fullcalendar/timegrid": "^5.7.2", "@mui/material": "^6.4.7", "@react-google-maps/api": "^2.12.2", "@reduxjs/toolkit": "^1.8.6", "axios": "^0.19.0", "bootstrap": "^5.3.2", "chart.js": "^3.7.1", "chartjs-plugin-trendline": "^2.0.2", "classnames": "^2.2.6", "file-saver": "^2.0.5", "formBuilder": "^3.8.3", "formik": "^2.2.9", "framer-motion": "^4.1.17", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.2", "html2canvas": "^1.4.1", "jquery": "^3.6.1", "jquery-ui-sortable": "^1.0.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "luxon": "^3.3.0", "moment": "^2.29.4", "ngrok": "^4.3.3", "pdfmake": "^0.2.7", "primeflex": "3.1.0", "primeicons": "^5.0.0", "primereact": "^9.5.0", "prismjs": "1.9.0", "quill": "^1.3.7", "react": "^17.0.1", "react-app-polyfill": "^1.0.6", "react-bigpicture": "^1.0.26", "react-bootstrap": "^2.9.1", "react-datepicker": "^4.23.0", "react-dom": "^17.0.1", "react-dropzone": "^14.3.8", "react-edit-text": "^5.0.2", "react-editext": "^5.1.0", "react-icons": "^4.6.0", "react-image-crop": "^10.0.8", "react-redux": "^8.0.4", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3", "react-sticky": "^6.0.3", "react-tiny-popover": "^7.2.0", "react-tooltip": "^4.5.1", "react-transition-group": "^4.4.1", "read-excel-file": "^5.5.0", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "sass": "^1.57.1", "sweetalert2": "^11.4.33", "uninstall": "^0.0.0", "use-force-update": "^1.0.10", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "overrides": {"@react-pdf/font": "2.2.1", "@react-pdf/pdfkit": "2.1.0"}}